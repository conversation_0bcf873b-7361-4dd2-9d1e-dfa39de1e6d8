#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于ui_integrated_w5.py的q_1208.ts文件表面保持四面体化处理脚本
使用TetGen的'pY'参数进行表面保持四面体化，并导出TS格式
"""

import numpy as np
import os
import sys
import time
from datetime import datetime

# 尝试导入必要的库
try:
    import pyvista as pv
    PYVISTA_AVAILABLE = True
    print("✓ PyVista 可用")
except ImportError:
    PYVISTA_AVAILABLE = False
    print("✗ PyVista 不可用")

try:
    import tetgen
    TETGEN_AVAILABLE = True
    print("✓ TetGen 可用")
except ImportError:
    TETGEN_AVAILABLE = False
    print("✗ TetGen 不可用，将使用PyVista备选方案")

def safe_execute(default_return=None, show_error=True):
    """安全执行装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if show_error:
                    print(f"错误 in {func.__name__}: {e}")
                return default_return
        return wrapper
    return decorator

@safe_execute(default_return=(np.array([]), np.array([])), show_error=True)
def read_tsurf_data(file_path):
    """读取TS文件（TSURF格式）数据"""
    print(f"正在读取文件: {file_path}")
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']  # 尝试多种编码
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}  # 用于映射顶点ID
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                lines = file.readlines()
                
            print(f"文件总行数: {len(lines)}")
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x = float(parts[2])
                            y = float(parts[3])
                            z = float(parts[4])
                            vrtx.append([x, y, z])
                            vrtx_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1 = int(parts[1])
                            v2 = int(parts[2])
                            v3 = int(parts[3])
                            
                            # 映射到实际的顶点索引
                            if v1 in vrtx_map and v2 in vrtx_map and v3 in vrtx_map:
                                trgl.append([vrtx_map[v1], vrtx_map[v2], vrtx_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                # 显示进度
                if (i + 1) % 10000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vrtx, dtype=np.float64)
            faces = np.array(trgl, dtype=np.int32)
            
            print(f"成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            print(f"使用编码: {encoding}")
            
            return vertices, faces
            
        except UnicodeDecodeError:
            print(f"编码 {encoding} 失败，尝试下一个...")
            continue
        except Exception as e:
            print(f"读取文件时出错 (编码 {encoding}): {e}")
            continue
    
    print("所有编码都失败了")
    return np.array([]), np.array([])

def minimal_mesh_cleanup(vertices, faces):
    """最小化网格清理：只处理严重的几何问题，保持原始形态"""
    print("进行最小化网格清理（保持原始形态）...")

    # 使用非常小的容差，只合并真正重复的顶点
    tolerance = 1e-12

    # 检查完全重复的顶点
    unique_vertices = []
    vertex_map = {}

    for i, vertex in enumerate(vertices):
        found_duplicate = False
        for j, unique_vertex in enumerate(unique_vertices):
            if np.allclose(vertex, unique_vertex, atol=tolerance):
                vertex_map[i] = j
                found_duplicate = True
                break

        if not found_duplicate:
            vertex_map[i] = len(unique_vertices)
            unique_vertices.append(vertex)

    unique_vertices = np.array(unique_vertices)
    removed_vertices = len(vertices) - len(unique_vertices)
    if removed_vertices > 0:
        print(f"移除完全重复顶点: {removed_vertices}")
    else:
        print("未发现完全重复顶点")

    # 更新面索引，只移除明显退化的面
    valid_faces = []
    removed_faces = 0

    for face in faces:
        try:
            new_face = [vertex_map[face[0]], vertex_map[face[1]], vertex_map[face[2]]]

            # 只检查最基本的退化情况
            if len(set(new_face)) == 3:  # 三个不同的顶点
                # 检查是否为极小面积（可能的数值误差）
                v0, v1, v2 = unique_vertices[new_face[0]], unique_vertices[new_face[1]], unique_vertices[new_face[2]]
                edge1 = v1 - v0
                edge2 = v2 - v0
                cross = np.cross(edge1, edge2)
                area = 0.5 * np.linalg.norm(cross)

                if area > 1e-15:  # 非常小的面积阈值
                    valid_faces.append(new_face)
                else:
                    removed_faces += 1
            else:
                removed_faces += 1

        except (KeyError, IndexError):
            removed_faces += 1
            continue

    valid_faces = np.array(valid_faces)

    if removed_faces > 0:
        print(f"移除退化面: {removed_faces}")
    else:
        print("未发现退化面")

    print(f"最终网格: {len(unique_vertices)} 顶点, {len(valid_faces)} 面")

    return unique_vertices, valid_faces

def repair_mesh_with_pyvista(vertices, faces):
    """使用PyVista修复网格"""
    if not PYVISTA_AVAILABLE:
        return vertices, faces

    print("使用PyVista修复网格...")

    try:
        # 创建PyVista网格
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])

        mesh = pv.PolyData(vertices, faces_with_header)
        print(f"原始网格: {mesh.n_points} 顶点, {mesh.n_cells} 面")

        # 清理网格
        mesh = mesh.clean(tolerance=1e-6)
        print(f"清理后: {mesh.n_points} 顶点, {mesh.n_cells} 面")

        # 移除重复面
        mesh = mesh.remove_duplicate_cells()

        # 填充小孔
        mesh = mesh.fill_holes(hole_size=100)

        # 修复法向量
        mesh = mesh.compute_normals(consistent_normals=True, auto_orient_normals=True)

        # 提取修复后的顶点和面
        repaired_vertices = mesh.points
        repaired_faces = []

        for i in range(mesh.n_cells):
            cell = mesh.get_cell(i)
            if cell.n_points == 3:
                repaired_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])

        repaired_faces = np.array(repaired_faces)
        print(f"修复完成: {len(repaired_vertices)} 顶点, {len(repaired_faces)} 面")

        return repaired_vertices, repaired_faces

    except Exception as e:
        print(f"PyVista修复失败: {e}")
        return vertices, faces

def create_tetrahedral_mesh_with_surface_preservation(vertices, faces, mesh_name="网格"):
    """
    使用表面保持方法创建四面体网格
    优先使用TetGen的'pY'参数进行表面保持四面体化
    """
    print(f"\n=== 开始{mesh_name}表面保持四面体化 ===")

    try:
        # 预处理：最小化清理（保持原始形态）
        clean_vertices, clean_faces = minimal_mesh_cleanup(vertices, faces)

        # 方法1: 直接使用TetGen表面保持方法（保持原始形态）
        if TETGEN_AVAILABLE:
            print("使用TetGen表面保持方法（保持原始形态）...")

            # 直接使用清理后的顶点和面，不进行额外的PyVista预处理
            print(f"输入TetGen: {len(clean_vertices)} 顶点, {len(clean_faces)} 面")

            # 使用TetGen表面保持四面体化
            tet = tetgen.TetGen(clean_vertices, clean_faces)
            print("TetGen对象创建成功")

            # 专注于表面保持的TetGen参数组合
            tetgen_options = [
                ('pY', '纯表面保持'),
                ('pYq2.0', '表面保持+宽松质量约束'),
                ('pYq3.0', '表面保持+更宽松质量约束'),
                ('pYA', '表面保持+自适应'),
                ('pYV', '表面保持+详细输出'),
                ('pq2.0', '质量约束(备选)')
            ]

            for option, description in tetgen_options:
                try:
                    print(f"尝试 {description} 参数: '{option}'")
                    if option:
                        tet.tetrahedralize(option)
                    else:
                        tet.tetrahedralize()

                    tetrahedral_mesh = tet.grid

                    if tetrahedral_mesh.n_cells > 0:
                        print(f"✓ {description} 成功: {tetrahedral_mesh.n_points} 顶点, {tetrahedral_mesh.n_cells} 四面体")
                        return tetrahedral_mesh
                    else:
                        print(f"✗ {description} 结果为空")

                except Exception as e:
                    print(f"✗ {description} 失败: {str(e)[:100]}...")
                    continue

        # 方法2: PyVista备选方案
        if PYVISTA_AVAILABLE:
            print("使用PyVista备选方案...")

            # 创建PyVista表面网格
            faces_with_header = []
            for face in clean_faces:
                faces_with_header.extend([3, face[0], face[1], face[2]])

            surface_mesh = pv.PolyData(clean_vertices, faces_with_header)
            print(f"PyVista表面网格: {surface_mesh.n_points} 顶点, {surface_mesh.n_cells} 面")

            # 清理和修复网格
            surface_mesh = surface_mesh.clean(tolerance=1e-6)
            surface_mesh = surface_mesh.fill_holes(hole_size=1000)

            # 尝试多种Delaunay参数
            delaunay_options = [
                {'alpha': 0, 'tol': 1e-06, 'offset': 2.5},
                {'alpha': 0, 'tol': 1e-05, 'offset': 5.0},
                {'alpha': 0.1, 'tol': 1e-06, 'offset': 2.5},
                {'alpha': 0, 'tol': 1e-04, 'offset': 10.0}
            ]

            for i, params in enumerate(delaunay_options):
                try:
                    print(f"尝试Delaunay参数组合 {i+1}: {params}")
                    tetrahedral_mesh = surface_mesh.delaunay_3d(**params)

                    if tetrahedral_mesh.n_cells > 0:
                        print(f"✓ PyVista四面体化成功: {tetrahedral_mesh.n_points} 顶点, {tetrahedral_mesh.n_cells} 四面体")
                        return tetrahedral_mesh
                    else:
                        print(f"✗ Delaunay参数组合 {i+1} 结果为空")

                except Exception as e:
                    print(f"✗ Delaunay参数组合 {i+1} 失败: {str(e)[:100]}...")
                    continue

        # 方法3: 简化网格后再尝试
        print("尝试简化网格后四面体化...")
        if PYVISTA_AVAILABLE:
            faces_with_header = []
            for face in clean_faces:
                faces_with_header.extend([3, face[0], face[1], face[2]])

            surface_mesh = pv.PolyData(clean_vertices, faces_with_header)

            # 简化网格
            simplified_mesh = surface_mesh.decimate(0.5)  # 减少50%的面
            print(f"简化网格: {simplified_mesh.n_points} 顶点, {simplified_mesh.n_cells} 面")

            try:
                tetrahedral_mesh = simplified_mesh.delaunay_3d(alpha=0, tol=1e-05, offset=5.0)
                if tetrahedral_mesh.n_cells > 0:
                    print(f"✓ 简化网格四面体化成功: {tetrahedral_mesh.n_points} 顶点, {tetrahedral_mesh.n_cells} 四面体")
                    return tetrahedral_mesh
            except Exception as e:
                print(f"✗ 简化网格四面体化失败: {e}")

        raise ValueError("所有四面体化方法都失败了")

    except Exception as e:
        print(f"四面体化过程出错: {e}")
        return None

def write_ts_file(file_path, vertices, faces, name=None):
    """将顶点和面信息写入标准GOCAD TS文件格式"""
    try:
        if name is None:
            name = os.path.splitext(os.path.basename(file_path))[0]
        
        print(f"正在写入TS文件: {file_path}")
        print(f"数据: {len(vertices)} 顶点, {len(faces)} 面")
        
        with open(file_path, "w", encoding='utf-8') as file:
            # 写入GOCAD TS文件头
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("use_feature_color: false\n")
            file.write("}\n")
            file.write("GOCAD_ORIGINAL_COORDINATE_SYSTEM\n")
            file.write("NAME \"Local\"\n")
            file.write("PROJECTION Unknown\n")
            file.write("DATUM Unknown\n")
            file.write("AXIS_NAME X Y Z\n")
            file.write("AXIS_UNIT m m m\n")
            file.write("ZPOSITIVE Elevation\n")
            file.write("END_ORIGINAL_COORDINATE_SYSTEM\n")
            file.write("TFACE\n")
            
            # 写入顶点(TS使用1-based索引)
            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            
            # 写入面(TS索引从1开始)
            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            # 写入结束标记
            file.write("END\n")
        
        print(f"✓ 成功写入TS文件: {os.path.basename(file_path)}")
        return True
    except Exception as e:
        print(f"写入TS文件失败: {e}")
        return False

def diagnose_mesh_problems(vertices, faces):
    """诊断网格问题"""
    print("\n=== 网格诊断 ===")

    # 检查重复顶点
    unique_vertices = np.unique(vertices, axis=0)
    duplicate_vertices = len(vertices) - len(unique_vertices)
    print(f"重复顶点: {duplicate_vertices}")

    # 检查退化面
    degenerate_faces = 0
    for face in faces:
        if len(set(face)) < 3:
            degenerate_faces += 1
    print(f"退化面: {degenerate_faces}")

    # 检查面的有效性
    invalid_faces = 0
    for face in faces:
        if any(idx >= len(vertices) or idx < 0 for idx in face):
            invalid_faces += 1
    print(f"无效面索引: {invalid_faces}")

    # 检查边界框
    bbox_size = vertices.max(axis=0) - vertices.min(axis=0)
    print(f"边界框大小: {bbox_size}")

    # 检查面积为零的三角形
    zero_area_faces = 0
    for face in faces:
        v0, v1, v2 = vertices[face[0]], vertices[face[1]], vertices[face[2]]
        edge1 = v1 - v0
        edge2 = v2 - v0
        cross = np.cross(edge1, edge2)
        area = 0.5 * np.linalg.norm(cross)
        if area < 1e-12:
            zero_area_faces += 1
    print(f"零面积三角形: {zero_area_faces}")

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts 表面保持四面体化处理脚本")
    print("基于ui_integrated_w5.py，使用TetGen 'pY'参数")
    print("增强版 - 包含网格修复和多重备选方案")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        return False

    print(f"输入文件: {input_file}")
    print(f"文件大小: {os.path.getsize(input_file) / (1024*1024):.2f} MB")

    # 创建输出目录
    output_dir = "tetrahedral_output"
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")

    start_time = time.time()

    # 步骤1: 读取TS文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_tsurf_data(input_file)

    if len(vertices) == 0 or len(faces) == 0:
        print("错误: 无法读取输入文件或文件为空")
        return False

    print(f"✓ 读取完成: {len(vertices)} 顶点, {len(faces)} 面")

    # 数据验证和诊断
    print(f"顶点坐标范围:")
    print(f"  X: {vertices[:, 0].min():.2f} ~ {vertices[:, 0].max():.2f}")
    print(f"  Y: {vertices[:, 1].min():.2f} ~ {vertices[:, 1].max():.2f}")
    print(f"  Z: {vertices[:, 2].min():.2f} ~ {vertices[:, 2].max():.2f}")

    # 诊断网格问题
    diagnose_mesh_problems(vertices, faces)
    
    # 步骤2: 表面保持四面体化
    print(f"\n步骤2: 表面保持四面体化...")
    tetrahedral_mesh = create_tetrahedral_mesh_with_surface_preservation(vertices, faces, "q_1208")
    
    if tetrahedral_mesh is None:
        print("错误: 四面体化失败")
        return False
    
    # 步骤3: 提取表面网格
    print(f"\n步骤3: 提取表面网格...")
    surface_mesh = tetrahedral_mesh.extract_surface()
    print(f"提取的表面: {surface_mesh.n_points} 顶点, {surface_mesh.n_cells} 面")
    
    # 转换为三角形网格
    surface_triangulated = surface_mesh.triangulate()
    print(f"三角化表面: {surface_triangulated.n_points} 顶点, {surface_triangulated.n_cells} 面")
    
    # 步骤4: 导出结果
    print(f"\n步骤4: 导出结果...")
    
    # 获取表面顶点和面
    surface_vertices = surface_triangulated.points
    surface_faces = []
    
    for i in range(surface_triangulated.n_cells):
        cell = surface_triangulated.get_cell(i)
        if cell.n_points == 3:  # 确保是三角形
            surface_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
    
    surface_faces = np.array(surface_faces)
    
    # 保存表面保持四面体化后的表面
    output_ts_file = os.path.join(output_dir, "q_1208_tetrahedral_surface.ts")
    success = write_ts_file(output_ts_file, surface_vertices, surface_faces, "q_1208_tetrahedral_surface")
    
    if success:
        print(f"✓ 表面保持四面体化完成")
        print(f"✓ 输出文件: {output_ts_file}")
    
    # 保存VTK格式用于可视化
    if PYVISTA_AVAILABLE:
        vtk_file = os.path.join(output_dir, "q_1208_tetrahedral_surface.vtk")
        surface_triangulated.save(vtk_file)
        print(f"✓ VTK文件: {vtk_file}")
        
        # 保存四面体网格
        tet_vtk_file = os.path.join(output_dir, "q_1208_tetrahedral_volume.vtk")
        tetrahedral_mesh.save(tet_vtk_file)
        print(f"✓ 四面体VTK文件: {tet_vtk_file}")
    
    # 统计信息
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"\n" + "=" * 60)
    print("处理完成统计:")
    print(f"原始数据: {len(vertices)} 顶点, {len(faces)} 面")
    print(f"四面体网格: {tetrahedral_mesh.n_points} 顶点, {tetrahedral_mesh.n_cells} 四面体")
    print(f"表面网格: {len(surface_vertices)} 顶点, {len(surface_faces)} 面")
    print(f"处理时间: {processing_time:.2f} 秒")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 脚本执行成功!")
            sys.exit(0)
        else:
            print("\n✗ 脚本执行失败!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n用户中断执行")
        sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        sys.exit(1)
