#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
q_1208.ts文件形态保持闭合脚本
在完全保持原始形态的前提下闭合TS模型，然后使用TetGen 'pY'参数
"""

import numpy as np
import os
import sys
import time
from datetime import datetime
from collections import defaultdict

# 尝试导入必要的库
try:
    import pyvista as pv
    PYVISTA_AVAILABLE = True
    print("✓ PyVista 可用")
except ImportError:
    PYVISTA_AVAILABLE = False
    print("✗ PyVista 不可用")
    sys.exit(1)

try:
    import tetgen
    TETGEN_AVAILABLE = True
    print("✓ TetGen 可用")
except ImportError:
    TETGEN_AVAILABLE = False
    print("✗ TetGen 不可用")
    sys.exit(1)

def read_tsurf_data(file_path):
    """读取TS文件数据"""
    print(f"正在读取文件: {file_path}")
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                lines = file.readlines()
                
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x = float(parts[2])
                            y = float(parts[3])
                            z = float(parts[4])
                            vrtx.append([x, y, z])
                            vrtx_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1 = int(parts[1])
                            v2 = int(parts[2])
                            v3 = int(parts[3])
                            
                            if v1 in vrtx_map and v2 in vrtx_map and v3 in vrtx_map:
                                trgl.append([vrtx_map[v1], vrtx_map[v2], vrtx_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                if (i + 1) % 10000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vrtx, dtype=np.float64)
            faces = np.array(trgl, dtype=np.int32)
            
            print(f"成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取文件时出错: {e}")
            continue
    
    return np.array([]), np.array([])

def analyze_boundary_structure(vertices, faces):
    """分析边界结构，找出需要闭合的孔洞"""
    print("\n=== 分析边界结构 ===")
    
    try:
        # 创建网格
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        
        mesh = pv.PolyData(vertices, faces_with_header)
        
        # 提取边界边
        boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                               non_manifold_edges=False, 
                                               manifold_edges=False)
        
        print(f"边界边数量: {boundaries.n_cells}")
        
        if boundaries.n_cells > 0:
            # 分析边界环
            boundary_loops = find_boundary_loops(boundaries)
            print(f"发现 {len(boundary_loops)} 个边界环")
            
            for i, loop in enumerate(boundary_loops):
                print(f"  边界环 {i+1}: {len(loop)} 条边")
            
            return boundaries, boundary_loops
        else:
            print("表面已经闭合")
            return None, []
            
    except Exception as e:
        print(f"边界分析失败: {e}")
        return None, []

def find_boundary_loops(boundaries):
    """找出边界环"""
    boundary_loops = []
    
    try:
        # 构建边界边的连接关系
        edge_connectivity = defaultdict(list)
        
        for i in range(boundaries.n_cells):
            edge = boundaries.get_cell(i)
            if edge.n_points == 2:
                p1, p2 = edge.point_ids[0], edge.point_ids[1]
                edge_connectivity[p1].append(p2)
                edge_connectivity[p2].append(p1)
        
        # 追踪边界环
        visited_points = set()
        
        for start_point in edge_connectivity:
            if start_point not in visited_points:
                loop = trace_boundary_loop(start_point, edge_connectivity, visited_points)
                if len(loop) > 2:  # 有效的环至少需要3个点
                    boundary_loops.append(loop)
        
        return boundary_loops
        
    except Exception as e:
        print(f"边界环查找失败: {e}")
        return []

def trace_boundary_loop(start_point, edge_connectivity, visited_points):
    """追踪一个边界环"""
    loop = [start_point]
    current_point = start_point
    visited_points.add(start_point)
    
    while True:
        # 找到下一个未访问的连接点
        next_points = [p for p in edge_connectivity[current_point] if p not in visited_points]
        
        if not next_points:
            # 尝试回到起始点
            if start_point in edge_connectivity[current_point]:
                break
            else:
                # 无法形成环
                break
        
        next_point = next_points[0]
        loop.append(next_point)
        visited_points.add(next_point)
        current_point = next_point
        
        # 防止无限循环
        if len(loop) > 10000:
            break
    
    return loop

def close_surface_preserving_shape(vertices, faces):
    """在保持形态的前提下闭合表面"""
    print("\n=== 形态保持表面闭合 ===")
    
    try:
        # 创建网格
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        
        mesh = pv.PolyData(vertices, faces_with_header)
        print(f"原始网格: {mesh.n_points} 顶点, {mesh.n_cells} 面")
        
        # 方法1: 最小孔洞填充（保持形态）
        print("方法1: 最小孔洞填充...")
        closed_mesh = minimal_hole_filling(mesh)
        
        if closed_mesh is not None:
            return extract_vertices_faces(closed_mesh)
        
        # 方法2: 边界三角化（保持形态）
        print("方法2: 边界三角化...")
        closed_mesh = boundary_triangulation(mesh)
        
        if closed_mesh is not None:
            return extract_vertices_faces(closed_mesh)
        
        # 方法3: 渐进式闭合（保持形态）
        print("方法3: 渐进式闭合...")
        closed_mesh = progressive_closing(mesh)
        
        if closed_mesh is not None:
            return extract_vertices_faces(closed_mesh)
        
        print("所有形态保持闭合方法都失败了")
        return None, None
        
    except Exception as e:
        print(f"形态保持闭合失败: {e}")
        return None, None

def minimal_hole_filling(mesh):
    """最小孔洞填充，只填充小孔洞"""
    try:
        print("  执行最小孔洞填充...")
        
        # 只填充非常小的孔洞，保持大的形态特征
        filled_mesh = mesh.fill_holes(hole_size=10)  # 很小的孔洞大小
        
        # 检查是否闭合
        boundaries = filled_mesh.extract_feature_edges(boundary_edges=True, 
                                                      non_manifold_edges=False, 
                                                      manifold_edges=False)
        
        print(f"    填充后边界边: {boundaries.n_cells}")
        
        if boundaries.n_cells == 0:
            print("  ✓ 最小孔洞填充成功闭合表面")
            return filled_mesh
        elif boundaries.n_cells < mesh.extract_feature_edges(boundary_edges=True, 
                                                            non_manifold_edges=False, 
                                                            manifold_edges=False).n_cells:
            print("  ⚠ 最小孔洞填充部分成功，继续尝试")
            # 递增填充更大的孔洞
            for hole_size in [50, 100, 200, 500]:
                try:
                    filled_mesh = mesh.fill_holes(hole_size=hole_size)
                    boundaries = filled_mesh.extract_feature_edges(boundary_edges=True, 
                                                                  non_manifold_edges=False, 
                                                                  manifold_edges=False)
                    print(f"    孔洞大小 {hole_size}: {boundaries.n_cells} 边界边")
                    
                    if boundaries.n_cells == 0:
                        print(f"  ✓ 孔洞大小 {hole_size} 成功闭合表面")
                        return filled_mesh
                except Exception as e:
                    print(f"    孔洞大小 {hole_size} 失败: {e}")
                    continue
        
        return None
        
    except Exception as e:
        print(f"  最小孔洞填充失败: {e}")
        return None

def boundary_triangulation(mesh):
    """边界三角化闭合"""
    try:
        print("  执行边界三角化...")
        
        # 提取边界
        boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                               non_manifold_edges=False, 
                                               manifold_edges=False)
        
        if boundaries.n_cells == 0:
            return mesh
        
        # 获取边界点
        boundary_points = boundaries.points
        
        # 计算边界中心点
        center = np.mean(boundary_points, axis=0)
        
        # 创建新的顶点列表（原始顶点 + 中心点）
        new_vertices = np.vstack([mesh.points, center.reshape(1, -1)])
        center_idx = len(mesh.points)
        
        # 创建新的面列表（原始面 + 边界三角化面）
        new_faces = []
        
        # 添加原始面
        for i in range(mesh.n_cells):
            cell = mesh.get_cell(i)
            if cell.n_points == 3:
                new_faces.extend([3, cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
        
        # 添加边界三角化面
        for i in range(boundaries.n_cells):
            edge = boundaries.get_cell(i)
            if edge.n_points == 2:
                p1, p2 = edge.point_ids[0], edge.point_ids[1]
                # 创建三角形连接边界边到中心点
                new_faces.extend([3, p1, p2, center_idx])
        
        # 创建新网格
        closed_mesh = pv.PolyData(new_vertices, new_faces)
        
        # 验证闭合性
        boundaries_check = closed_mesh.extract_feature_edges(boundary_edges=True, 
                                                            non_manifold_edges=False, 
                                                            manifold_edges=False)
        
        print(f"    三角化后边界边: {boundaries_check.n_cells}")
        
        if boundaries_check.n_cells == 0:
            print("  ✓ 边界三角化成功闭合表面")
            return closed_mesh
        else:
            print("  ✗ 边界三角化未能完全闭合")
            return None
        
    except Exception as e:
        print(f"  边界三角化失败: {e}")
        return None

def progressive_closing(mesh):
    """渐进式闭合"""
    try:
        print("  执行渐进式闭合...")
        
        current_mesh = mesh
        max_iterations = 10
        
        for iteration in range(max_iterations):
            # 检查当前边界
            boundaries = current_mesh.extract_feature_edges(boundary_edges=True, 
                                                           non_manifold_edges=False, 
                                                           manifold_edges=False)
            
            print(f"    迭代 {iteration + 1}: {boundaries.n_cells} 边界边")
            
            if boundaries.n_cells == 0:
                print("  ✓ 渐进式闭合成功")
                return current_mesh
            
            # 尝试填充小孔洞
            hole_size = 20 * (iteration + 1)  # 逐渐增大孔洞大小
            try:
                current_mesh = current_mesh.fill_holes(hole_size=hole_size)
            except Exception as e:
                print(f"    迭代 {iteration + 1} 失败: {e}")
                break
        
        print("  ✗ 渐进式闭合未能完全闭合")
        return None
        
    except Exception as e:
        print(f"  渐进式闭合失败: {e}")
        return None

def extract_vertices_faces(mesh):
    """从PyVista网格提取顶点和面"""
    vertices = mesh.points
    faces = []
    
    for i in range(mesh.n_cells):
        cell = mesh.get_cell(i)
        if cell.n_points == 3:
            faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
    
    faces = np.array(faces)
    return vertices, faces

def verify_closure_and_shape_preservation(original_vertices, original_faces, closed_vertices, closed_faces):
    """验证闭合性和形态保持"""
    print("\n=== 验证闭合性和形态保持 ===")
    
    try:
        # 验证闭合性
        faces_with_header = []
        for face in closed_faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        
        closed_mesh = pv.PolyData(closed_vertices, faces_with_header)
        
        boundaries = closed_mesh.extract_feature_edges(boundary_edges=True, 
                                                      non_manifold_edges=False, 
                                                      manifold_edges=False)
        
        non_manifold = closed_mesh.extract_feature_edges(boundary_edges=False, 
                                                        non_manifold_edges=True, 
                                                        manifold_edges=False)
        
        print(f"闭合验证:")
        print(f"  边界边数: {boundaries.n_cells}")
        print(f"  非流形边数: {non_manifold.n_cells}")
        
        is_closed = (boundaries.n_cells == 0 and non_manifold.n_cells == 0)
        
        if is_closed:
            print("✓ 表面成功闭合")
        else:
            print("⚠ 表面仍有问题")
        
        # 验证形态保持
        print(f"形态保持验证:")
        print(f"  原始: {len(original_vertices)} 顶点, {len(original_faces)} 面")
        print(f"  闭合后: {len(closed_vertices)} 顶点, {len(closed_faces)} 面")
        
        added_vertices = len(closed_vertices) - len(original_vertices)
        added_faces = len(closed_faces) - len(original_faces)
        
        print(f"  新增: {added_vertices} 顶点, {added_faces} 面")
        
        if added_vertices < len(original_vertices) * 0.1 and added_faces < len(original_faces) * 0.1:
            print("✓ 形态基本保持（新增几何 < 10%）")
            shape_preserved = True
        else:
            print("⚠ 形态变化较大")
            shape_preserved = False
        
        return is_closed, shape_preserved
        
    except Exception as e:
        print(f"验证失败: {e}")
        return False, False

def tetgen_pY_only(vertices, faces):
    """使用TetGen 'pY'参数进行四面体化"""
    print("\n=== TetGen 'pY'参数四面体化 ===")
    
    try:
        print("创建TetGen对象...")
        tet = tetgen.TetGen(vertices, faces)
        
        print("执行'pY'参数四面体化...")
        tet.tetrahedralize('pY')
        
        tetrahedral_mesh = tet.grid
        
        if tetrahedral_mesh.n_cells > 0:
            print(f"✓ 'pY'参数四面体化成功!")
            print(f"  结果: {tetrahedral_mesh.n_points} 顶点, {tetrahedral_mesh.n_cells} 四面体")
            return tetrahedral_mesh
        else:
            print("✗ 'pY'参数四面体化结果为空")
            return None
            
    except Exception as e:
        print(f"✗ 'pY'参数四面体化失败: {e}")
        return None

def write_ts_file(file_path, vertices, faces, name=None):
    """写入TS文件"""
    try:
        if name is None:
            name = os.path.splitext(os.path.basename(file_path))[0]

        print(f"正在写入TS文件: {file_path}")

        with open(file_path, "w", encoding='utf-8') as file:
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("}\n")
            file.write("TFACE\n")

            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")

            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")

            file.write("END\n")

        print(f"✓ 成功写入TS文件")
        return True
    except Exception as e:
        print(f"写入TS文件失败: {e}")
        return False

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts 形态保持闭合脚本")
    print("在完全保持原始形态的前提下闭合TS模型")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        return False

    # 创建输出目录
    output_dir = "shape_preserving_closure_output"
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")

    start_time = time.time()

    # 步骤1: 读取文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_tsurf_data(input_file)

    if len(vertices) == 0 or len(faces) == 0:
        print("错误: 无法读取输入文件")
        return False

    print(f"✓ 读取完成: {len(vertices)} 顶点, {len(faces)} 面")

    # 步骤2: 分析边界结构
    print(f"\n步骤2: 分析边界结构...")
    boundaries, boundary_loops = analyze_boundary_structure(vertices, faces)

    if boundaries is None:
        print("表面已经闭合，无需处理")
        return True

    # 步骤3: 形态保持闭合
    print(f"\n步骤3: 形态保持闭合...")
    closed_vertices, closed_faces = close_surface_preserving_shape(vertices, faces)

    if closed_vertices is None or closed_faces is None:
        print("错误: 形态保持闭合失败")
        return False

    # 步骤4: 验证闭合性和形态保持
    print(f"\n步骤4: 验证闭合性和形态保持...")
    is_closed, shape_preserved = verify_closure_and_shape_preservation(
        vertices, faces, closed_vertices, closed_faces)

    if not is_closed:
        print("警告: 表面未完全闭合")

    if not shape_preserved:
        print("警告: 形态变化较大")

    # 保存闭合后的表面
    closed_ts_file = os.path.join(output_dir, "q_1208_shape_preserved_closed.ts")
    write_ts_file(closed_ts_file, closed_vertices, closed_faces, "q_1208_shape_preserved_closed")

    # 步骤5: TetGen 'pY'四面体化
    print(f"\n步骤5: TetGen 'pY'四面体化...")
    tet_mesh = tetgen_pY_only(closed_vertices, closed_faces)

    if tet_mesh is None:
        print("错误: 'pY'参数四面体化失败")
        return False

    # 步骤6: 提取表面并保存
    print(f"\n步骤6: 提取表面并保存...")

    # 提取表面
    surface = tet_mesh.extract_surface()
    surface = surface.triangulate()

    # 获取表面数据
    surface_vertices = surface.points
    surface_faces = []
    for i in range(surface.n_cells):
        cell = surface.get_cell(i)
        if cell.n_points == 3:
            surface_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])

    surface_faces = np.array(surface_faces)

    # 保存最终结果
    output_ts_file = os.path.join(output_dir, "q_1208_final_shape_preserved.ts")
    success = write_ts_file(output_ts_file, surface_vertices, surface_faces, "q_1208_final_shape_preserved")

    # 保存VTK文件
    surface.save(os.path.join(output_dir, "q_1208_shape_preserved_surface.vtk"))
    tet_mesh.save(os.path.join(output_dir, "q_1208_shape_preserved_volume.vtk"))

    # 统计信息
    end_time = time.time()
    print(f"\n" + "=" * 60)
    print("形态保持闭合和TetGen 'pY'参数四面体化完成:")
    print(f"原始网格: {len(vertices)} 顶点, {len(faces)} 面")
    print(f"闭合后: {len(closed_vertices)} 顶点, {len(closed_faces)} 面")
    print(f"四面体网格: {tet_mesh.n_points} 顶点, {tet_mesh.n_cells} 四面体")
    print(f"最终表面: {len(surface_vertices)} 顶点, {len(surface_faces)} 面")
    print(f"处理时间: {end_time - start_time:.2f} 秒")
    print(f"闭合表面文件: {closed_ts_file}")
    print(f"最终结果文件: {output_ts_file}")

    if is_closed and shape_preserved:
        print("✓ 成功在保持形态的前提下闭合表面并完成四面体化!")
    elif is_closed:
        print("✓ 成功闭合表面并完成四面体化（形态有轻微变化）")
    else:
        print("⚠ 完成四面体化但表面可能仍有问题")

    print("=" * 60)

    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 形态保持闭合脚本执行成功!")
            sys.exit(0)
        else:
            print("\n✗ 形态保持闭合脚本执行失败!")
            sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        sys.exit(1)
