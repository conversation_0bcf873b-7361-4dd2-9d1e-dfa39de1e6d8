#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
q_1208.ts文件凸包重建和TetGen 'pY'参数四面体化脚本
使用凸包创建可靠的闭合表面，然后使用TetGen 'pY'参数
"""

import numpy as np
import os
import sys
import time
from datetime import datetime

# 尝试导入必要的库
try:
    import pyvista as pv
    PYVISTA_AVAILABLE = True
    print("✓ PyVista 可用")
except ImportError:
    PYVISTA_AVAILABLE = False
    print("✗ PyVista 不可用")
    sys.exit(1)

try:
    import tetgen
    TETGEN_AVAILABLE = True
    print("✓ TetGen 可用")
except ImportError:
    TETGEN_AVAILABLE = False
    print("✗ TetGen 不可用")
    sys.exit(1)

def read_tsurf_data(file_path):
    """读取TS文件数据"""
    print(f"正在读取文件: {file_path}")
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                lines = file.readlines()
                
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x = float(parts[2])
                            y = float(parts[3])
                            z = float(parts[4])
                            vrtx.append([x, y, z])
                            vrtx_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1 = int(parts[1])
                            v2 = int(parts[2])
                            v3 = int(parts[3])
                            
                            if v1 in vrtx_map and v2 in vrtx_map and v3 in vrtx_map:
                                trgl.append([vrtx_map[v1], vrtx_map[v2], vrtx_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                if (i + 1) % 10000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vrtx, dtype=np.float64)
            faces = np.array(trgl, dtype=np.int32)
            
            print(f"成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取文件时出错: {e}")
            continue
    
    return np.array([]), np.array([])

def create_convex_hull_surface(vertices):
    """创建凸包闭合表面"""
    print("\n=== 创建凸包闭合表面 ===")
    
    try:
        # 创建点云
        point_cloud = pv.PolyData(vertices)
        print(f"输入点云: {point_cloud.n_points} 顶点")
        
        # 生成凸包
        print("生成凸包...")
        convex_hull = point_cloud.delaunay_3d(alpha=0, tol=1e-06, offset=0.1)
        
        # 提取表面
        surface = convex_hull.extract_surface()
        print(f"凸包表面: {surface.n_points} 顶点, {surface.n_cells} 面")
        
        # 清理表面
        print("清理凸包表面...")
        surface = surface.clean(tolerance=1e-8)
        surface = surface.triangulate()
        
        # 验证闭合性
        boundaries = surface.extract_feature_edges(boundary_edges=True, 
                                                  non_manifold_edges=False, 
                                                  manifold_edges=False)
        
        non_manifold = surface.extract_feature_edges(boundary_edges=False, 
                                                    non_manifold_edges=True, 
                                                    manifold_edges=False)
        
        print(f"验证结果:")
        print(f"  边界边数: {boundaries.n_cells}")
        print(f"  非流形边数: {non_manifold.n_cells}")
        
        if boundaries.n_cells == 0 and non_manifold.n_cells == 0:
            print("✓ 凸包表面是完美的闭合流形")
        elif boundaries.n_cells == 0:
            print("✓ 凸包表面闭合但有少量非流形边")
        else:
            print("⚠ 凸包表面仍有问题，但继续尝试")
        
        # 提取顶点和面
        hull_vertices = surface.points
        hull_faces = []
        
        for i in range(surface.n_cells):
            cell = surface.get_cell(i)
            if cell.n_points == 3:
                hull_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
        
        hull_faces = np.array(hull_faces)
        
        print(f"凸包重建完成: {len(hull_vertices)} 顶点, {len(hull_faces)} 面")
        
        return hull_vertices, hull_faces
        
    except Exception as e:
        print(f"凸包重建失败: {e}")
        return None, None

def tetgen_pY_with_convex_hull(vertices, faces):
    """使用凸包表面进行TetGen 'pY'四面体化"""
    print("\n=== TetGen 'pY'参数四面体化（凸包表面）===")
    
    try:
        print("创建TetGen对象...")
        tet = tetgen.TetGen(vertices, faces)
        
        print("执行'pY'参数四面体化...")
        print("  'p' - 保持输入点")
        print("  'Y' - 保持输入表面")
        
        tet.tetrahedralize('pY')
        
        tetrahedral_mesh = tet.grid
        
        if tetrahedral_mesh.n_cells > 0:
            print(f"✓ 'pY'参数四面体化成功!")
            print(f"  结果: {tetrahedral_mesh.n_points} 顶点, {tetrahedral_mesh.n_cells} 四面体")
            return tetrahedral_mesh
        else:
            print("✗ 'pY'参数四面体化结果为空")
            return None
            
    except Exception as e:
        print(f"✗ 'pY'参数四面体化失败: {e}")
        return None

def write_ts_file(file_path, vertices, faces, name=None):
    """写入TS文件"""
    try:
        if name is None:
            name = os.path.splitext(os.path.basename(file_path))[0]
        
        print(f"正在写入TS文件: {file_path}")
        
        with open(file_path, "w", encoding='utf-8') as file:
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("}\n")
            file.write("TFACE\n")
            
            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            
            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            file.write("END\n")
        
        print(f"✓ 成功写入TS文件")
        return True
    except Exception as e:
        print(f"写入TS文件失败: {e}")
        return False

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts 凸包重建和TetGen 'pY'参数四面体化脚本")
    print("使用凸包创建可靠的闭合表面")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        return False
    
    # 创建输出目录
    output_dir = "convex_hull_pY_output"
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")
    
    start_time = time.time()
    
    # 步骤1: 读取文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_tsurf_data(input_file)
    
    if len(vertices) == 0 or len(faces) == 0:
        print("错误: 无法读取输入文件")
        return False
    
    print(f"✓ 读取完成: {len(vertices)} 顶点, {len(faces)} 面")
    
    # 步骤2: 创建凸包表面
    print(f"\n步骤2: 创建凸包表面...")
    hull_vertices, hull_faces = create_convex_hull_surface(vertices)
    
    if hull_vertices is None or hull_faces is None:
        print("错误: 凸包创建失败")
        return False
    
    # 保存凸包表面
    hull_ts_file = os.path.join(output_dir, "q_1208_convex_hull.ts")
    write_ts_file(hull_ts_file, hull_vertices, hull_faces, "q_1208_convex_hull")
    
    # 步骤3: TetGen 'pY'四面体化
    print(f"\n步骤3: TetGen 'pY'四面体化...")
    tet_mesh = tetgen_pY_with_convex_hull(hull_vertices, hull_faces)
    
    if tet_mesh is None:
        print("错误: 'pY'参数四面体化失败")
        return False
    
    # 步骤4: 提取表面并保存
    print(f"\n步骤4: 提取表面并保存...")
    
    # 提取表面
    surface = tet_mesh.extract_surface()
    surface = surface.triangulate()
    
    # 获取表面数据
    surface_vertices = surface.points
    surface_faces = []
    for i in range(surface.n_cells):
        cell = surface.get_cell(i)
        if cell.n_points == 3:
            surface_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
    
    surface_faces = np.array(surface_faces)
    
    # 保存最终结果
    output_ts_file = os.path.join(output_dir, "q_1208_convex_hull_pY_result.ts")
    success = write_ts_file(output_ts_file, surface_vertices, surface_faces, "q_1208_convex_hull_pY_result")
    
    # 保存VTK文件
    surface.save(os.path.join(output_dir, "q_1208_convex_hull_surface.vtk"))
    tet_mesh.save(os.path.join(output_dir, "q_1208_convex_hull_volume.vtk"))
    
    # 统计信息
    end_time = time.time()
    print(f"\n" + "=" * 60)
    print("凸包重建和TetGen 'pY'参数四面体化完成:")
    print(f"原始网格: {len(vertices)} 顶点, {len(faces)} 面")
    print(f"凸包表面: {len(hull_vertices)} 顶点, {len(hull_faces)} 面")
    print(f"四面体网格: {tet_mesh.n_points} 顶点, {tet_mesh.n_cells} 四面体")
    print(f"最终表面: {len(surface_vertices)} 顶点, {len(surface_faces)} 面")
    print(f"处理时间: {end_time - start_time:.2f} 秒")
    print(f"凸包表面文件: {hull_ts_file}")
    print(f"最终结果文件: {output_ts_file}")
    print("✓ 成功使用凸包+TetGen 'pY'参数完成四面体化!")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 凸包+'pY'参数脚本执行成功!")
            sys.exit(0)
        else:
            print("\n✗ 凸包+'pY'参数脚本执行失败!")
            sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        sys.exit(1)
