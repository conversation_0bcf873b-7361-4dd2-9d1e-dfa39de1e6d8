#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
q_1208.ts文件PyVista Delaunay 3D四面体化脚本
当TetGen失败时的可靠备选方案
"""

import numpy as np
import os
import sys
import time
from datetime import datetime

# 尝试导入必要的库
try:
    import pyvista as pv
    PYVISTA_AVAILABLE = True
    print("✓ PyVista 可用")
except ImportError:
    PYVISTA_AVAILABLE = False
    print("✗ PyVista 不可用")
    sys.exit(1)

def read_tsurf_data(file_path):
    """读取TS文件数据"""
    print(f"正在读取文件: {file_path}")
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                lines = file.readlines()
                
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x = float(parts[2])
                            y = float(parts[3])
                            z = float(parts[4])
                            vrtx.append([x, y, z])
                            vrtx_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1 = int(parts[1])
                            v2 = int(parts[2])
                            v3 = int(parts[3])
                            
                            if v1 in vrtx_map and v2 in vrtx_map and v3 in vrtx_map:
                                trgl.append([vrtx_map[v1], vrtx_map[v2], vrtx_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                if (i + 1) % 10000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vrtx, dtype=np.float64)
            faces = np.array(trgl, dtype=np.int32)
            
            print(f"成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取文件时出错: {e}")
            continue
    
    return np.array([]), np.array([])

def create_tetrahedral_mesh_with_delaunay(vertices, faces):
    """使用PyVista Delaunay 3D创建四面体网格"""
    print("\n=== PyVista Delaunay 3D 四面体化 ===")
    
    try:
        # 创建PyVista表面网格
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        
        surface_mesh = pv.PolyData(vertices, faces_with_header)
        print(f"输入表面网格: {surface_mesh.n_points} 顶点, {surface_mesh.n_cells} 面")
        
        # 清理网格
        print("清理表面网格...")
        surface_mesh = surface_mesh.clean(tolerance=1e-6)
        print(f"清理后: {surface_mesh.n_points} 顶点, {surface_mesh.n_cells} 面")
        
        # 尝试多种Delaunay 3D参数组合
        delaunay_configs = [
            {'alpha': 0, 'tol': 1e-05, 'offset': 2.5, 'bound': False},
            {'alpha': 0, 'tol': 1e-04, 'offset': 5.0, 'bound': False},
            {'alpha': 0, 'tol': 1e-03, 'offset': 10.0, 'bound': False},
            {'alpha': 0.1, 'tol': 1e-05, 'offset': 2.5, 'bound': False},
            {'alpha': 0, 'tol': 1e-05, 'offset': 2.5, 'bound': True},
            {'alpha': 0, 'tol': 1e-04, 'offset': 5.0, 'bound': True}
        ]
        
        for i, config in enumerate(delaunay_configs):
            try:
                print(f"\n尝试配置 {i+1}: {config}")
                
                # 执行Delaunay 3D四面体化
                tetrahedral_mesh = surface_mesh.delaunay_3d(**config)
                
                if tetrahedral_mesh.n_cells > 0:
                    print(f"✓ 配置 {i+1} 成功!")
                    print(f"  四面体网格: {tetrahedral_mesh.n_points} 顶点, {tetrahedral_mesh.n_cells} 四面体")
                    return tetrahedral_mesh
                else:
                    print(f"✗ 配置 {i+1} 结果为空")
                    
            except Exception as e:
                print(f"✗ 配置 {i+1} 失败: {str(e)[:100]}...")
                continue
        
        # 如果所有配置都失败，尝试使用点云方法
        print("\n尝试点云Delaunay方法...")
        try:
            # 只使用顶点创建点云
            point_cloud = pv.PolyData(vertices)
            tetrahedral_mesh = point_cloud.delaunay_3d(alpha=0, tol=1e-03, offset=10.0)
            
            if tetrahedral_mesh.n_cells > 0:
                print(f"✓ 点云方法成功!")
                print(f"  四面体网格: {tetrahedral_mesh.n_points} 顶点, {tetrahedral_mesh.n_cells} 四面体")
                return tetrahedral_mesh
            else:
                print("✗ 点云方法结果为空")
                
        except Exception as e:
            print(f"✗ 点云方法失败: {e}")
        
        print("所有Delaunay方法都失败了")
        return None
        
    except Exception as e:
        print(f"Delaunay四面体化出错: {e}")
        return None

def write_ts_file(file_path, vertices, faces, name=None):
    """写入TS文件"""
    try:
        if name is None:
            name = os.path.splitext(os.path.basename(file_path))[0]
        
        print(f"正在写入TS文件: {file_path}")
        
        with open(file_path, "w", encoding='utf-8') as file:
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("}\n")
            file.write("TFACE\n")
            
            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            
            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            file.write("END\n")
        
        print(f"✓ 成功写入TS文件")
        return True
    except Exception as e:
        print(f"写入TS文件失败: {e}")
        return False

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts PyVista Delaunay 3D 四面体化脚本")
    print("TetGen失败时的可靠备选方案")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        return False
    
    # 创建输出目录
    output_dir = "delaunay_output"
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")
    
    start_time = time.time()
    
    # 步骤1: 读取文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_tsurf_data(input_file)
    
    if len(vertices) == 0 or len(faces) == 0:
        print("错误: 无法读取输入文件")
        return False
    
    print(f"✓ 读取完成: {len(vertices)} 顶点, {len(faces)} 面")
    
    # 数据统计
    print(f"顶点坐标范围:")
    print(f"  X: {vertices[:, 0].min():.2f} ~ {vertices[:, 0].max():.2f}")
    print(f"  Y: {vertices[:, 1].min():.2f} ~ {vertices[:, 1].max():.2f}")
    print(f"  Z: {vertices[:, 2].min():.2f} ~ {vertices[:, 2].max():.2f}")
    
    # 步骤2: Delaunay 3D四面体化
    print(f"\n步骤2: Delaunay 3D四面体化...")
    tet_mesh = create_tetrahedral_mesh_with_delaunay(vertices, faces)
    
    if tet_mesh is None:
        print("错误: Delaunay四面体化失败")
        return False
    
    # 步骤3: 提取表面并保存
    print(f"\n步骤3: 提取表面并保存...")
    
    # 提取表面
    surface = tet_mesh.extract_surface()
    surface = surface.triangulate()
    
    print(f"提取的表面: {surface.n_points} 顶点, {surface.n_cells} 面")
    
    # 获取表面数据
    surface_vertices = surface.points
    surface_faces = []
    for i in range(surface.n_cells):
        cell = surface.get_cell(i)
        if cell.n_points == 3:
            surface_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
    
    surface_faces = np.array(surface_faces)
    
    # 保存TS文件
    output_ts_file = os.path.join(output_dir, "q_1208_delaunay_surface.ts")
    success = write_ts_file(output_ts_file, surface_vertices, surface_faces, "q_1208_delaunay_surface")
    
    if success:
        # 保存VTK文件
        surface.save(os.path.join(output_dir, "q_1208_delaunay_surface.vtk"))
        tet_mesh.save(os.path.join(output_dir, "q_1208_delaunay_volume.vtk"))
        
        # 统计信息
        end_time = time.time()
        print(f"\n" + "=" * 60)
        print("Delaunay 3D四面体化完成:")
        print(f"原始网格: {len(vertices)} 顶点, {len(faces)} 面")
        print(f"四面体网格: {tet_mesh.n_points} 顶点, {tet_mesh.n_cells} 四面体")
        print(f"提取表面: {len(surface_vertices)} 顶点, {len(surface_faces)} 面")
        print(f"处理时间: {end_time - start_time:.2f} 秒")
        print(f"输出文件: {output_ts_file}")
        print("=" * 60)
        
        return True
    else:
        print("保存文件失败")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ Delaunay脚本执行成功!")
            sys.exit(0)
        else:
            print("\n✗ Delaunay脚本执行失败!")
            sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        sys.exit(1)
