#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
q_1208.ts文件纯TetGen 'pY'参数四面体化脚本
专门使用TetGen的'pY'参数，通过表面修复确保成功
"""

import numpy as np
import os
import sys
import time
from datetime import datetime

# 尝试导入必要的库
try:
    import pyvista as pv
    PYVISTA_AVAILABLE = True
    print("✓ PyVista 可用")
except ImportError:
    PYVISTA_AVAILABLE = False
    print("✗ PyVista 不可用")

try:
    import tetgen
    TETGEN_AVAILABLE = True
    print("✓ TetGen 可用")
except ImportError:
    TETGEN_AVAILABLE = False
    print("✗ TetGen 不可用")
    sys.exit(1)

def read_tsurf_data(file_path):
    """读取TS文件数据"""
    print(f"正在读取文件: {file_path}")
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                lines = file.readlines()
                
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x = float(parts[2])
                            y = float(parts[3])
                            z = float(parts[4])
                            vrtx.append([x, y, z])
                            vrtx_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1 = int(parts[1])
                            v2 = int(parts[2])
                            v3 = int(parts[3])
                            
                            if v1 in vrtx_map and v2 in vrtx_map and v3 in vrtx_map:
                                trgl.append([vrtx_map[v1], vrtx_map[v2], vrtx_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                if (i + 1) % 10000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vrtx, dtype=np.float64)
            faces = np.array(trgl, dtype=np.int32)
            
            print(f"成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取文件时出错: {e}")
            continue
    
    return np.array([]), np.array([])

def diagnose_surface_for_pY(vertices, faces):
    """诊断表面是否适合TetGen 'pY'参数"""
    print("\n=== 表面诊断（为'pY'参数） ===")
    
    if not PYVISTA_AVAILABLE:
        print("PyVista不可用，无法进行详细诊断")
        return
    
    try:
        # 创建网格
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        
        mesh = pv.PolyData(vertices, faces_with_header)
        
        # 检查1: 边界边（开放表面）
        boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                               non_manifold_edges=False, 
                                               manifold_edges=False)
        print(f"边界边数量: {boundaries.n_cells}")
        if boundaries.n_cells > 0:
            print("  ⚠ 表面不闭合，这会导致'pY'失败")
        else:
            print("  ✓ 表面闭合")
        
        # 检查2: 非流形边
        non_manifold = mesh.extract_feature_edges(boundary_edges=False, 
                                                 non_manifold_edges=True, 
                                                 manifold_edges=False)
        print(f"非流形边数量: {non_manifold.n_cells}")
        if non_manifold.n_cells > 0:
            print("  ⚠ 存在非流形边，这会导致'pY'失败")
        else:
            print("  ✓ 表面为流形")
        
        # 检查3: 表面法向量一致性
        mesh_with_normals = mesh.compute_normals()
        normals = mesh_with_normals.point_normals
        
        # 简单检查法向量是否大致一致
        avg_normal = np.mean(normals, axis=0)
        avg_normal = avg_normal / np.linalg.norm(avg_normal)
        
        consistent_normals = 0
        for normal in normals:
            if np.dot(normal, avg_normal) > 0:
                consistent_normals += 1
        
        consistency_ratio = consistent_normals / len(normals)
        print(f"法向量一致性: {consistency_ratio:.2%}")
        if consistency_ratio < 0.8:
            print("  ⚠ 法向量不一致，可能影响'pY'")
        else:
            print("  ✓ 法向量基本一致")
        
        # 总结
        is_suitable = (boundaries.n_cells == 0 and 
                      non_manifold.n_cells == 0 and 
                      consistency_ratio > 0.8)
        
        if is_suitable:
            print("  ✓ 表面适合TetGen 'pY'参数")
        else:
            print("  ⚠ 表面需要修复才能使用'pY'参数")
        
        return is_suitable
        
    except Exception as e:
        print(f"诊断失败: {e}")
        return False

def repair_surface_for_pY(vertices, faces):
    """专门为TetGen 'pY'参数修复表面"""
    print("\n=== 为'pY'参数修复表面 ===")
    
    if not PYVISTA_AVAILABLE:
        print("PyVista不可用，返回原始数据")
        return vertices, faces
    
    try:
        # 创建网格
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        
        mesh = pv.PolyData(vertices, faces_with_header)
        print(f"原始网格: {mesh.n_points} 顶点, {mesh.n_cells} 面")
        
        # 修复步骤1: 基础清理
        print("步骤1: 基础清理...")
        mesh = mesh.clean(tolerance=1e-8)
        print(f"  清理后: {mesh.n_points} 顶点, {mesh.n_cells} 面")
        
        # 修复步骤2: 填充所有孔洞（确保闭合）
        print("步骤2: 填充孔洞...")
        original_cells = mesh.n_cells
        mesh = mesh.fill_holes(hole_size=100000)  # 填充所有孔洞
        added_faces = mesh.n_cells - original_cells
        print(f"  填充了 {added_faces} 个面，总计: {mesh.n_cells} 面")
        
        # 修复步骤3: 确保法向量一致
        print("步骤3: 修复法向量...")
        mesh = mesh.compute_normals(consistent_normals=True, auto_orient_normals=True)
        
        # 修复步骤4: 最终清理
        print("步骤4: 最终清理...")
        mesh = mesh.clean(tolerance=1e-9)
        
        # 验证修复结果
        print("步骤5: 验证修复结果...")
        boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                               non_manifold_edges=False, 
                                               manifold_edges=False)
        non_manifold = mesh.extract_feature_edges(boundary_edges=False, 
                                                 non_manifold_edges=True, 
                                                 manifold_edges=False)
        
        print(f"  修复后边界边: {boundaries.n_cells}")
        print(f"  修复后非流形边: {non_manifold.n_cells}")
        
        if boundaries.n_cells == 0 and non_manifold.n_cells == 0:
            print("  ✓ 表面修复成功，适合'pY'参数")
        else:
            print("  ⚠ 表面仍有问题，但继续尝试")
        
        # 提取修复后的数据
        repaired_vertices = mesh.points
        repaired_faces = []
        
        for i in range(mesh.n_cells):
            cell = mesh.get_cell(i)
            if cell.n_points == 3:
                repaired_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
        
        repaired_faces = np.array(repaired_faces)
        
        print(f"修复完成: {len(repaired_vertices)} 顶点, {len(repaired_faces)} 面")
        
        return repaired_vertices, repaired_faces
        
    except Exception as e:
        print(f"表面修复失败: {e}")
        return vertices, faces

def tetgen_pY_only(vertices, faces, description=""):
    """只使用TetGen 'pY'参数进行四面体化"""
    print(f"\n=== TetGen 'pY'参数四面体化 {description} ===")
    
    try:
        print("创建TetGen对象...")
        tet = tetgen.TetGen(vertices, faces)
        
        print("执行'pY'参数四面体化...")
        print("  'p' - 保持输入点")
        print("  'Y' - 保持输入表面")
        
        # 只使用'pY'参数
        tet.tetrahedralize('pY')
        
        tetrahedral_mesh = tet.grid
        
        if tetrahedral_mesh.n_cells > 0:
            print(f"✓ 'pY'参数四面体化成功!")
            print(f"  结果: {tetrahedral_mesh.n_points} 顶点, {tetrahedral_mesh.n_cells} 四面体")
            return tetrahedral_mesh
        else:
            print("✗ 'pY'参数四面体化结果为空")
            return None
            
    except Exception as e:
        error_msg = str(e)
        print(f"✗ 'pY'参数四面体化失败:")
        
        if "Failed to tetrahedralize" in error_msg:
            if "manifold" in error_msg:
                print("  原因: 表面不是流形")
                print("  建议: 需要修复表面使其成为闭合流形")
            elif "self-intersections" in error_msg:
                print("  原因: 表面存在自相交")
                print("  建议: 需要修复自相交问题")
            elif "input triangles are skipped" in error_msg:
                print("  原因: 部分三角形被跳过")
                print("  建议: 存在重复或无效的三角形")
            else:
                print(f"  原因: {error_msg}")
        else:
            print(f"  错误: {error_msg}")
        
        return None

def write_ts_file(file_path, vertices, faces, name=None):
    """写入TS文件"""
    try:
        if name is None:
            name = os.path.splitext(os.path.basename(file_path))[0]
        
        print(f"正在写入TS文件: {file_path}")
        
        with open(file_path, "w", encoding='utf-8') as file:
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("}\n")
            file.write("TFACE\n")
            
            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            
            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            file.write("END\n")
        
        print(f"✓ 成功写入TS文件")
        return True
    except Exception as e:
        print(f"写入TS文件失败: {e}")
        return False

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts 纯TetGen 'pY'参数四面体化脚本")
    print("专门使用TetGen的'pY'参数进行表面保持四面体化")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        return False
    
    # 创建输出目录
    output_dir = "pure_pY_output"
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")
    
    start_time = time.time()
    
    # 步骤1: 读取文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_tsurf_data(input_file)
    
    if len(vertices) == 0 or len(faces) == 0:
        print("错误: 无法读取输入文件")
        return False
    
    print(f"✓ 读取完成: {len(vertices)} 顶点, {len(faces)} 面")
    
    # 步骤2: 诊断表面
    print(f"\n步骤2: 诊断表面...")
    is_suitable = diagnose_surface_for_pY(vertices, faces)
    
    # 步骤3: 尝试直接使用'pY'
    print(f"\n步骤3: 尝试直接使用'pY'参数...")
    tet_mesh = tetgen_pY_only(vertices, faces, "(原始数据)")
    
    # 如果失败，修复表面后再试
    if tet_mesh is None:
        print(f"\n步骤4: 修复表面后再次尝试'pY'...")
        repaired_vertices, repaired_faces = repair_surface_for_pY(vertices, faces)
        
        # 保存修复后的表面
        repaired_ts_file = os.path.join(output_dir, "q_1208_repaired_for_pY.ts")
        write_ts_file(repaired_ts_file, repaired_vertices, repaired_faces, "q_1208_repaired_for_pY")
        
        # 再次诊断
        print(f"\n重新诊断修复后的表面...")
        diagnose_surface_for_pY(repaired_vertices, repaired_faces)
        
        # 使用修复后的表面尝试'pY'
        tet_mesh = tetgen_pY_only(repaired_vertices, repaired_faces, "(修复后)")
    
    if tet_mesh is None:
        print("错误: 'pY'参数四面体化失败")
        return False
    
    # 步骤5: 提取表面并保存
    print(f"\n步骤5: 提取表面并保存...")
    
    if PYVISTA_AVAILABLE:
        # 提取表面
        surface = tet_mesh.extract_surface()
        surface = surface.triangulate()
        
        # 获取表面数据
        surface_vertices = surface.points
        surface_faces = []
        for i in range(surface.n_cells):
            cell = surface.get_cell(i)
            if cell.n_points == 3:
                surface_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
        
        surface_faces = np.array(surface_faces)
        
        # 保存最终结果
        output_ts_file = os.path.join(output_dir, "q_1208_pY_result.ts")
        success = write_ts_file(output_ts_file, surface_vertices, surface_faces, "q_1208_pY_result")
        
        # 保存VTK文件
        surface.save(os.path.join(output_dir, "q_1208_pY_surface.vtk"))
        tet_mesh.save(os.path.join(output_dir, "q_1208_pY_volume.vtk"))
        
        # 统计信息
        end_time = time.time()
        print(f"\n" + "=" * 60)
        print("TetGen 'pY'参数四面体化完成:")
        print(f"原始网格: {len(vertices)} 顶点, {len(faces)} 面")
        print(f"四面体网格: {tet_mesh.n_points} 顶点, {tet_mesh.n_cells} 四面体")
        print(f"提取表面: {len(surface_vertices)} 顶点, {len(surface_faces)} 面")
        print(f"处理时间: {end_time - start_time:.2f} 秒")
        print(f"输出文件: {output_ts_file}")
        print("✓ 成功使用TetGen 'pY'参数完成表面保持四面体化!")
        print("=" * 60)
        
        return True
    else:
        print("PyVista不可用，无法提取表面")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 纯'pY'参数脚本执行成功!")
            sys.exit(0)
        else:
            print("\n✗ 纯'pY'参数脚本执行失败!")
            sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        sys.exit(1)
