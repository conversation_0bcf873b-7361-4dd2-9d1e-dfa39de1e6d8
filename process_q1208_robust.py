#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
q_1208.ts文件鲁棒性四面体化处理脚本
专门处理有几何问题的网格，使用多种修复策略
"""

import numpy as np
import os
import sys
import time
from datetime import datetime

# 尝试导入必要的库
try:
    import pyvista as pv
    PYVISTA_AVAILABLE = True
    print("✓ PyVista 可用")
except ImportError:
    PYVISTA_AVAILABLE = False
    print("✗ PyVista 不可用")

try:
    import tetgen
    TETGEN_AVAILABLE = True
    print("✓ TetGen 可用")
except ImportError:
    TETGEN_AVAILABLE = False
    print("✗ TetGen 不可用")

def safe_execute(default_return=None, show_error=True):
    """安全执行装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if show_error:
                    print(f"错误 in {func.__name__}: {e}")
                return default_return
        return wrapper
    return decorator

@safe_execute(default_return=(np.array([]), np.array([])), show_error=True)
def read_tsurf_data(file_path):
    """读取TS文件数据"""
    print(f"正在读取文件: {file_path}")
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                lines = file.readlines()
                
            print(f"文件总行数: {len(lines)}")
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x = float(parts[2])
                            y = float(parts[3])
                            z = float(parts[4])
                            vrtx.append([x, y, z])
                            vrtx_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1 = int(parts[1])
                            v2 = int(parts[2])
                            v3 = int(parts[3])
                            
                            if v1 in vrtx_map and v2 in vrtx_map and v3 in vrtx_map:
                                trgl.append([vrtx_map[v1], vrtx_map[v2], vrtx_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                if (i + 1) % 10000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vrtx, dtype=np.float64)
            faces = np.array(trgl, dtype=np.int32)
            
            print(f"成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取文件时出错: {e}")
            continue
    
    return np.array([]), np.array([])

def robust_mesh_repair(vertices, faces):
    """鲁棒性网格修复"""
    print("\n=== 鲁棒性网格修复 ===")
    
    # 第一步：基础清理
    print("步骤1: 基础几何清理...")
    tolerance = 1e-5  # 使用较大的容差
    
    # 合并近似重复顶点
    unique_vertices = []
    vertex_map = {}
    
    for i, vertex in enumerate(vertices):
        found_duplicate = False
        for j, unique_vertex in enumerate(unique_vertices):
            if np.linalg.norm(vertex - unique_vertex) < tolerance:
                vertex_map[i] = j
                found_duplicate = True
                break
        
        if not found_duplicate:
            vertex_map[i] = len(unique_vertices)
            unique_vertices.append(vertex)
    
    unique_vertices = np.array(unique_vertices)
    print(f"顶点去重: {len(vertices)} -> {len(unique_vertices)}")
    
    # 更新面并过滤无效面
    valid_faces = []
    for face in faces:
        try:
            new_face = [vertex_map[face[0]], vertex_map[face[1]], vertex_map[face[2]]]
            # 检查面的有效性
            if len(set(new_face)) == 3:  # 三个不同顶点
                # 检查面积
                v0, v1, v2 = unique_vertices[new_face[0]], unique_vertices[new_face[1]], unique_vertices[new_face[2]]
                edge1 = v1 - v0
                edge2 = v2 - v0
                cross = np.cross(edge1, edge2)
                area = 0.5 * np.linalg.norm(cross)
                if area > 1e-10:  # 面积足够大
                    valid_faces.append(new_face)
        except (KeyError, IndexError):
            continue
    
    valid_faces = np.array(valid_faces)
    print(f"面清理: {len(faces)} -> {len(valid_faces)}")
    
    return unique_vertices, valid_faces

def create_simple_tetrahedral_mesh(vertices, faces):
    """创建简单的四面体网格，专注于成功率而非质量"""
    print("\n=== 简单四面体化 ===")
    
    if not PYVISTA_AVAILABLE:
        print("PyVista不可用，无法进行四面体化")
        return None
    
    try:
        # 创建PyVista网格
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        
        mesh = pv.PolyData(vertices, faces_with_header)
        print(f"输入网格: {mesh.n_points} 顶点, {mesh.n_cells} 面")
        
        # 激进的网格简化
        print("激进简化网格...")
        simplified = mesh.decimate(0.8)  # 减少80%的面
        print(f"简化后: {simplified.n_points} 顶点, {simplified.n_cells} 面")
        
        # 清理简化后的网格
        simplified = simplified.clean(tolerance=1e-4)
        simplified = simplified.remove_duplicate_cells()
        
        # 使用宽松的参数进行四面体化
        print("使用宽松参数进行四面体化...")
        tet_mesh = simplified.delaunay_3d(alpha=0, tol=1e-3, offset=10.0)
        
        if tet_mesh.n_cells > 0:
            print(f"✓ 简单四面体化成功: {tet_mesh.n_points} 顶点, {tet_mesh.n_cells} 四面体")
            return tet_mesh
        else:
            print("✗ 简单四面体化失败")
            return None
            
    except Exception as e:
        print(f"简单四面体化出错: {e}")
        return None

def write_ts_file(file_path, vertices, faces, name=None):
    """写入TS文件"""
    try:
        if name is None:
            name = os.path.splitext(os.path.basename(file_path))[0]
        
        print(f"正在写入TS文件: {file_path}")
        
        with open(file_path, "w", encoding='utf-8') as file:
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("}\n")
            file.write("TFACE\n")
            
            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            
            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            file.write("END\n")
        
        print(f"✓ 成功写入TS文件: {os.path.basename(file_path)}")
        return True
    except Exception as e:
        print(f"写入TS文件失败: {e}")
        return False

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts 鲁棒性四面体化处理脚本")
    print("专门处理有几何问题的网格")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        return False
    
    # 创建输出目录
    output_dir = "robust_output"
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")
    
    start_time = time.time()
    
    # 读取文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_tsurf_data(input_file)
    
    if len(vertices) == 0 or len(faces) == 0:
        print("错误: 无法读取输入文件")
        return False
    
    print(f"✓ 读取完成: {len(vertices)} 顶点, {len(faces)} 面")
    
    # 鲁棒性修复
    print(f"\n步骤2: 鲁棒性网格修复...")
    clean_vertices, clean_faces = robust_mesh_repair(vertices, faces)
    
    if len(clean_vertices) == 0 or len(clean_faces) == 0:
        print("错误: 网格修复失败")
        return False
    
    # 保存清理后的网格
    clean_ts_file = os.path.join(output_dir, "q_1208_cleaned.ts")
    write_ts_file(clean_ts_file, clean_vertices, clean_faces, "q_1208_cleaned")
    
    # 简单四面体化
    print(f"\n步骤3: 简单四面体化...")
    tet_mesh = create_simple_tetrahedral_mesh(clean_vertices, clean_faces)
    
    if tet_mesh is not None:
        # 提取表面
        surface = tet_mesh.extract_surface()
        surface = surface.triangulate()
        
        # 获取表面数据
        surface_vertices = surface.points
        surface_faces = []
        for i in range(surface.n_cells):
            cell = surface.get_cell(i)
            if cell.n_points == 3:
                surface_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
        
        surface_faces = np.array(surface_faces)
        
        # 保存结果
        output_ts_file = os.path.join(output_dir, "q_1208_tetrahedral_robust.ts")
        success = write_ts_file(output_ts_file, surface_vertices, surface_faces, "q_1208_tetrahedral_robust")
        
        if PYVISTA_AVAILABLE:
            # 保存VTK文件
            surface.save(os.path.join(output_dir, "q_1208_surface_robust.vtk"))
            tet_mesh.save(os.path.join(output_dir, "q_1208_volume_robust.vtk"))
        
        # 统计
        end_time = time.time()
        print(f"\n" + "=" * 60)
        print("处理完成:")
        print(f"原始: {len(vertices)} 顶点, {len(faces)} 面")
        print(f"清理后: {len(clean_vertices)} 顶点, {len(clean_faces)} 面")
        print(f"四面体: {tet_mesh.n_points} 顶点, {tet_mesh.n_cells} 四面体")
        print(f"表面: {len(surface_vertices)} 顶点, {len(surface_faces)} 面")
        print(f"处理时间: {end_time - start_time:.2f} 秒")
        print("=" * 60)
        
        return True
    else:
        print("四面体化失败")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 鲁棒性脚本执行成功!")
            sys.exit(0)
        else:
            print("\n✗ 鲁棒性脚本执行失败!")
            sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        sys.exit(1)
