#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
q_1208.ts文件非流形表面修复和四面体化脚本
专门处理非流形表面问题，然后使用TetGen 'pY'参数
"""

import numpy as np
import os
import sys
import time
from datetime import datetime
from collections import defaultdict

# 尝试导入必要的库
try:
    import pyvista as pv
    PYVISTA_AVAILABLE = True
    print("✓ PyVista 可用")
except ImportError:
    PYVISTA_AVAILABLE = False
    print("✗ PyVista 不可用")

try:
    import tetgen
    TETGEN_AVAILABLE = True
    print("✓ TetGen 可用")
except ImportError:
    TETGEN_AVAILABLE = False
    print("✗ TetGen 不可用")

def read_tsurf_data(file_path):
    """读取TS文件数据"""
    print(f"正在读取文件: {file_path}")
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                lines = file.readlines()
                
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x = float(parts[2])
                            y = float(parts[3])
                            z = float(parts[4])
                            vrtx.append([x, y, z])
                            vrtx_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1 = int(parts[1])
                            v2 = int(parts[2])
                            v3 = int(parts[3])
                            
                            if v1 in vrtx_map and v2 in vrtx_map and v3 in vrtx_map:
                                trgl.append([vrtx_map[v1], vrtx_map[v2], vrtx_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                if (i + 1) % 10000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vrtx, dtype=np.float64)
            faces = np.array(trgl, dtype=np.int32)
            
            print(f"成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取文件时出错: {e}")
            continue
    
    return np.array([]), np.array([])

def analyze_manifold_issues(vertices, faces):
    """分析非流形问题"""
    print("\n=== 分析非流形问题 ===")
    
    # 构建边-面映射
    edge_face_map = defaultdict(list)
    
    for face_idx, face in enumerate(faces):
        # 为每个面的三条边建立映射
        edges = [
            tuple(sorted([face[0], face[1]])),
            tuple(sorted([face[1], face[2]])),
            tuple(sorted([face[2], face[0]]))
        ]
        
        for edge in edges:
            edge_face_map[edge].append(face_idx)
    
    # 检查非流形边（被超过2个面共享的边）
    non_manifold_edges = []
    boundary_edges = []
    
    for edge, face_list in edge_face_map.items():
        if len(face_list) > 2:
            non_manifold_edges.append(edge)
        elif len(face_list) == 1:
            boundary_edges.append(edge)
    
    print(f"非流形边数量: {len(non_manifold_edges)}")
    print(f"边界边数量: {len(boundary_edges)}")
    print(f"总边数: {len(edge_face_map)}")
    
    # 检查重复面
    face_set = set()
    duplicate_faces = []
    
    for i, face in enumerate(faces):
        sorted_face = tuple(sorted(face))
        if sorted_face in face_set:
            duplicate_faces.append(i)
        else:
            face_set.add(sorted_face)
    
    print(f"重复面数量: {len(duplicate_faces)}")
    
    return non_manifold_edges, boundary_edges, duplicate_faces

def repair_manifold_with_pyvista(vertices, faces):
    """使用PyVista修复非流形表面"""
    print("\n=== 使用PyVista修复非流形表面 ===")
    
    if not PYVISTA_AVAILABLE:
        print("PyVista不可用，无法修复")
        return vertices, faces
    
    try:
        # 创建PyVista网格
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        
        mesh = pv.PolyData(vertices, faces_with_header)
        print(f"原始网格: {mesh.n_points} 顶点, {mesh.n_cells} 面")
        
        # 步骤1: 清理网格
        print("步骤1: 清理网格...")
        mesh = mesh.clean(tolerance=1e-6)
        print(f"清理后: {mesh.n_points} 顶点, {mesh.n_cells} 面")
        
        # 步骤2: 手动移除重复面（兼容性处理）
        print("步骤2: 手动移除重复面...")
        try:
            # 尝试新版本方法
            mesh = mesh.remove_duplicate_cells()
        except AttributeError:
            # 手动去重面
            print("使用手动去重方法...")
            unique_faces = []
            face_set = set()

            for i in range(mesh.n_cells):
                cell = mesh.get_cell(i)
                if cell.n_points == 3:
                    face_tuple = tuple(sorted([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]]))
                    if face_tuple not in face_set:
                        face_set.add(face_tuple)
                        unique_faces.append([3, cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])

            # 重建网格
            if unique_faces:
                faces_array = []
                for face in unique_faces:
                    faces_array.extend(face)
                mesh = pv.PolyData(mesh.points, faces_array)

        print(f"去重后: {mesh.n_points} 顶点, {mesh.n_cells} 面")

        # 步骤3: 填充孔洞
        print("步骤3: 填充孔洞...")
        try:
            mesh = mesh.fill_holes(hole_size=100)  # 增大孔洞大小
            print(f"填孔后: {mesh.n_points} 顶点, {mesh.n_cells} 面")
        except Exception as e:
            print(f"填孔失败: {e}，跳过此步骤")

        # 步骤3.5: 尝试闭合表面
        print("步骤3.5: 尝试闭合表面...")
        try:
            # 检查是否为闭合表面
            boundaries = mesh.extract_feature_edges(boundary_edges=True,
                                                   non_manifold_edges=False,
                                                   manifold_edges=False)
            if boundaries.n_cells > 0:
                print(f"发现 {boundaries.n_cells} 条边界边，尝试闭合...")
                # 尝试更大的孔洞填充
                mesh = mesh.fill_holes(hole_size=1000)
                print(f"大孔填充后: {mesh.n_points} 顶点, {mesh.n_cells} 面")
        except Exception as e:
            print(f"闭合表面失败: {e}")
        
        # 步骤4: 修复法向量
        print("步骤4: 修复法向量...")
        mesh = mesh.compute_normals(consistent_normals=True, auto_orient_normals=True)
        
        # 步骤5: 检查是否为流形
        print("步骤5: 检查流形性...")
        try:
            # 尝试提取边界
            boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                                   non_manifold_edges=False, 
                                                   manifold_edges=False)
            print(f"边界边数: {boundaries.n_cells}")
            
            # 检查非流形边
            non_manifold = mesh.extract_feature_edges(boundary_edges=False, 
                                                     non_manifold_edges=True, 
                                                     manifold_edges=False)
            print(f"非流形边数: {non_manifold.n_cells}")
            
        except Exception as e:
            print(f"流形性检查失败: {e}")
        
        # 提取修复后的顶点和面
        repaired_vertices = mesh.points
        repaired_faces = []
        
        for i in range(mesh.n_cells):
            cell = mesh.get_cell(i)
            if cell.n_points == 3:
                repaired_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
        
        repaired_faces = np.array(repaired_faces)
        
        print(f"修复完成: {len(repaired_vertices)} 顶点, {len(repaired_faces)} 面")
        
        return repaired_vertices, repaired_faces
        
    except Exception as e:
        print(f"PyVista修复失败: {e}")
        return vertices, faces

def create_closed_surface_with_convex_hull(vertices, faces):
    """使用凸包创建闭合表面（备选方案）"""
    print("\n=== 创建凸包闭合表面（备选方案）===")

    if not PYVISTA_AVAILABLE:
        return vertices, faces

    try:
        # 创建点云
        points = pv.PolyData(vertices)

        # 生成凸包
        convex_hull = points.delaunay_3d().extract_surface()
        print(f"凸包表面: {convex_hull.n_points} 顶点, {convex_hull.n_cells} 面")

        # 提取凸包的顶点和面
        hull_vertices = convex_hull.points
        hull_faces = []

        for i in range(convex_hull.n_cells):
            cell = convex_hull.get_cell(i)
            if cell.n_points == 3:
                hull_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])

        hull_faces = np.array(hull_faces)

        return hull_vertices, hull_faces

    except Exception as e:
        print(f"凸包创建失败: {e}")
        return vertices, faces

def tetgen_with_multiple_strategies(vertices, faces):
    """使用多种策略进行TetGen四面体化"""
    print("\n=== TetGen多策略四面体化 ===")

    if not TETGEN_AVAILABLE:
        print("TetGen不可用!")
        return None

    # 策略1: 直接使用修复后的表面
    print("\n策略1: 直接使用修复后的表面")
    result = try_tetgen_with_options(vertices, faces, "修复表面")
    if result is not None:
        return result

    # 策略2: 使用凸包表面
    print("\n策略2: 使用凸包表面")
    hull_vertices, hull_faces = create_closed_surface_with_convex_hull(vertices, faces)
    result = try_tetgen_with_options(hull_vertices, hull_faces, "凸包表面")
    if result is not None:
        return result

    # 策略3: 简化后再尝试
    if PYVISTA_AVAILABLE:
        print("\n策略3: 简化表面后尝试")
        try:
            faces_with_header = []
            for face in faces:
                faces_with_header.extend([3, face[0], face[1], face[2]])

            mesh = pv.PolyData(vertices, faces_with_header)
            simplified = mesh.decimate(0.7)  # 减少30%的面

            # 提取简化后的数据
            simp_vertices = simplified.points
            simp_faces = []
            for i in range(simplified.n_cells):
                cell = simplified.get_cell(i)
                if cell.n_points == 3:
                    simp_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])

            simp_faces = np.array(simp_faces)
            print(f"简化表面: {len(simp_vertices)} 顶点, {len(simp_faces)} 面")

            result = try_tetgen_with_options(simp_vertices, simp_faces, "简化表面")
            if result is not None:
                return result

        except Exception as e:
            print(f"简化策略失败: {e}")

    print("所有策略都失败了")
    return None

def try_tetgen_with_options(vertices, faces, strategy_name):
    """尝试使用不同TetGen参数"""
    try:
        # 创建TetGen对象
        tet = tetgen.TetGen(vertices, faces)
        print(f"{strategy_name} - TetGen对象创建成功")

        # 尝试不同参数（从最宽松到最严格）
        tetgen_options = [
            ('q1.8', '质量约束1.8（最宽松）'),
            ('q1.5', '质量约束1.5'),
            ('pq2.0', '保持点+质量约束2.0'),
            ('pq1.8', '保持点+质量约束1.8'),
            ('pYq2.0', '表面保持+质量约束2.0'),
            ('pY', '纯表面保持'),
            ('p', '仅保持输入点'),
            ('', '默认参数')
        ]
        
        for option, description in tetgen_options:
            try:
                print(f"  尝试 {description} (参数: '{option}')...")

                # 重新创建TetGen对象
                tet = tetgen.TetGen(vertices, faces)

                # 执行四面体化
                if option:
                    tet.tetrahedralize(option)
                else:
                    tet.tetrahedralize()

                tetrahedral_mesh = tet.grid

                if tetrahedral_mesh.n_cells > 0:
                    print(f"  ✓ {strategy_name} - {description} 成功!")
                    print(f"    结果: {tetrahedral_mesh.n_points} 顶点, {tetrahedral_mesh.n_cells} 四面体")
                    return tetrahedral_mesh
                else:
                    print(f"  ✗ {description} 结果为空")

            except Exception as e:
                error_msg = str(e)
                if len(error_msg) > 50:
                    error_msg = error_msg[:50] + "..."
                print(f"  ✗ {description} 失败: {error_msg}")
                continue

        print(f"{strategy_name} - 所有参数都失败了")
        return None

    except Exception as e:
        print(f"{strategy_name} - TetGen处理出错: {e}")
        return None

def write_ts_file(file_path, vertices, faces, name=None):
    """写入TS文件"""
    try:
        if name is None:
            name = os.path.splitext(os.path.basename(file_path))[0]
        
        print(f"正在写入TS文件: {file_path}")
        
        with open(file_path, "w", encoding='utf-8') as file:
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("}\n")
            file.write("TFACE\n")
            
            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            
            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            file.write("END\n")
        
        print(f"✓ 成功写入TS文件")
        return True
    except Exception as e:
        print(f"写入TS文件失败: {e}")
        return False

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts 非流形表面修复和四面体化脚本")
    print("修复非流形问题后使用TetGen 'pY'参数")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        return False
    
    # 创建输出目录
    output_dir = "manifold_repair_output"
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")
    
    start_time = time.time()
    
    # 步骤1: 读取文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_tsurf_data(input_file)
    
    if len(vertices) == 0 or len(faces) == 0:
        print("错误: 无法读取输入文件")
        return False
    
    print(f"✓ 读取完成: {len(vertices)} 顶点, {len(faces)} 面")
    
    # 步骤2: 分析非流形问题
    print(f"\n步骤2: 分析非流形问题...")
    non_manifold_edges, boundary_edges, duplicate_faces = analyze_manifold_issues(vertices, faces)
    
    # 步骤3: 修复非流形表面
    print(f"\n步骤3: 修复非流形表面...")
    repaired_vertices, repaired_faces = repair_manifold_with_pyvista(vertices, faces)
    
    # 保存修复后的网格
    repaired_ts_file = os.path.join(output_dir, "q_1208_manifold_repaired.ts")
    write_ts_file(repaired_ts_file, repaired_vertices, repaired_faces, "q_1208_manifold_repaired")
    
    # 步骤4: 重新分析修复后的网格
    print(f"\n步骤4: 重新分析修复后的网格...")
    analyze_manifold_issues(repaired_vertices, repaired_faces)
    
    # 步骤5: 多策略TetGen四面体化
    print(f"\n步骤5: 多策略TetGen四面体化...")
    tet_mesh = tetgen_with_multiple_strategies(repaired_vertices, repaired_faces)
    
    if tet_mesh is None:
        print("错误: 四面体化失败")
        return False
    
    # 步骤6: 提取表面并保存
    print(f"\n步骤6: 提取表面并保存...")
    
    if PYVISTA_AVAILABLE:
        # 提取表面
        surface = tet_mesh.extract_surface()
        surface = surface.triangulate()
        
        # 获取表面数据
        surface_vertices = surface.points
        surface_faces = []
        for i in range(surface.n_cells):
            cell = surface.get_cell(i)
            if cell.n_points == 3:
                surface_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
        
        surface_faces = np.array(surface_faces)
        
        # 保存最终结果
        output_ts_file = os.path.join(output_dir, "q_1208_tetrahedral_manifold.ts")
        success = write_ts_file(output_ts_file, surface_vertices, surface_faces, "q_1208_tetrahedral_manifold")
        
        # 保存VTK文件
        surface.save(os.path.join(output_dir, "q_1208_surface_manifold.vtk"))
        tet_mesh.save(os.path.join(output_dir, "q_1208_volume_manifold.vtk"))
        
        # 统计信息
        end_time = time.time()
        print(f"\n" + "=" * 60)
        print("非流形修复和四面体化完成:")
        print(f"原始网格: {len(vertices)} 顶点, {len(faces)} 面")
        print(f"修复后: {len(repaired_vertices)} 顶点, {len(repaired_faces)} 面")
        print(f"四面体网格: {tet_mesh.n_points} 顶点, {tet_mesh.n_cells} 四面体")
        print(f"最终表面: {len(surface_vertices)} 顶点, {len(surface_faces)} 面")
        print(f"处理时间: {end_time - start_time:.2f} 秒")
        print(f"输出文件: {output_ts_file}")
        print("=" * 60)
        
        return True
    else:
        print("PyVista不可用，无法提取表面")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 非流形修复脚本执行成功!")
            sys.exit(0)
        else:
            print("\n✗ 非流形修复脚本执行失败!")
            sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        sys.exit(1)
