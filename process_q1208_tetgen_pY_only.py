#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
q_1208.ts文件专用TetGen 'pY'参数四面体化脚本
专门解决TetGen 'pY'参数失败的问题，确保表面流形性
"""

import numpy as np
import os
import sys
import time
from datetime import datetime
from collections import defaultdict

# 尝试导入必要的库
try:
    import pyvista as pv
    PYVISTA_AVAILABLE = True
    print("✓ PyVista 可用")
except ImportError:
    PYVISTA_AVAILABLE = False
    print("✗ PyVista 不可用")

try:
    import tetgen
    TETGEN_AVAILABLE = True
    print("✓ TetGen 可用")
except ImportError:
    TETGEN_AVAILABLE = False
    print("✗ TetGen 不可用")
    sys.exit(1)

def read_tsurf_data(file_path):
    """读取TS文件数据"""
    print(f"正在读取文件: {file_path}")
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                lines = file.readlines()
                
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x = float(parts[2])
                            y = float(parts[3])
                            z = float(parts[4])
                            vrtx.append([x, y, z])
                            vrtx_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1 = int(parts[1])
                            v2 = int(parts[2])
                            v3 = int(parts[3])
                            
                            if v1 in vrtx_map and v2 in vrtx_map and v3 in vrtx_map:
                                trgl.append([vrtx_map[v1], vrtx_map[v2], vrtx_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                if (i + 1) % 10000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vrtx, dtype=np.float64)
            faces = np.array(trgl, dtype=np.int32)
            
            print(f"成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取文件时出错: {e}")
            continue
    
    return np.array([]), np.array([])

def make_surface_manifold_for_tetgen(vertices, faces):
    """专门为TetGen 'pY'参数制作流形表面"""
    print("\n=== 为TetGen 'pY'参数制作流形表面 ===")
    
    if not PYVISTA_AVAILABLE:
        print("PyVista不可用，无法修复表面")
        return vertices, faces
    
    try:
        # 步骤1: 创建PyVista网格
        print("步骤1: 创建PyVista网格...")
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        
        mesh = pv.PolyData(vertices, faces_with_header)
        print(f"原始网格: {mesh.n_points} 顶点, {mesh.n_cells} 面")
        
        # 步骤2: 基础清理
        print("步骤2: 基础清理...")
        mesh = mesh.clean(tolerance=1e-8)  # 使用更小的容差
        print(f"清理后: {mesh.n_points} 顶点, {mesh.n_cells} 面")
        
        # 步骤3: 检查并修复边界
        print("步骤3: 检查边界...")
        try:
            boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                                   non_manifold_edges=False, 
                                                   manifold_edges=False)
            print(f"边界边数: {boundaries.n_cells}")
            
            if boundaries.n_cells > 0:
                print("发现开放边界，尝试闭合...")
                # 尝试填充所有孔洞
                mesh = mesh.fill_holes(hole_size=10000)  # 大孔洞填充
                print(f"填孔后: {mesh.n_points} 顶点, {mesh.n_cells} 面")
                
                # 再次检查边界
                boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                                       non_manifold_edges=False, 
                                                       manifold_edges=False)
                print(f"填孔后边界边数: {boundaries.n_cells}")
        except Exception as e:
            print(f"边界检查失败: {e}")
        
        # 步骤4: 检查非流形边
        print("步骤4: 检查非流形边...")
        try:
            non_manifold = mesh.extract_feature_edges(boundary_edges=False, 
                                                     non_manifold_edges=True, 
                                                     manifold_edges=False)
            print(f"非流形边数: {non_manifold.n_cells}")
            
            if non_manifold.n_cells > 0:
                print("发现非流形边，尝试修复...")
                # 使用更激进的清理
                mesh = mesh.clean(tolerance=1e-6)
                mesh = mesh.triangulate()
                print(f"修复后: {mesh.n_points} 顶点, {mesh.n_cells} 面")
        except Exception as e:
            print(f"非流形检查失败: {e}")
        
        # 步骤5: 确保法向量一致性
        print("步骤5: 修复法向量...")
        mesh = mesh.compute_normals(consistent_normals=True, auto_orient_normals=True)
        
        # 步骤6: 最终验证
        print("步骤6: 最终验证...")
        try:
            # 检查是否为闭合流形
            boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                                   non_manifold_edges=False, 
                                                   manifold_edges=False)
            non_manifold = mesh.extract_feature_edges(boundary_edges=False, 
                                                     non_manifold_edges=True, 
                                                     manifold_edges=False)
            
            print(f"最终检查 - 边界边: {boundaries.n_cells}, 非流形边: {non_manifold.n_cells}")
            
            if boundaries.n_cells == 0 and non_manifold.n_cells == 0:
                print("✓ 表面已成功修复为闭合流形")
            else:
                print("⚠ 表面仍有问题，但继续尝试TetGen")
                
        except Exception as e:
            print(f"最终验证失败: {e}")
        
        # 提取修复后的顶点和面
        repaired_vertices = mesh.points
        repaired_faces = []
        
        for i in range(mesh.n_cells):
            cell = mesh.get_cell(i)
            if cell.n_points == 3:
                repaired_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
        
        repaired_faces = np.array(repaired_faces)
        
        print(f"修复完成: {len(repaired_vertices)} 顶点, {len(repaired_faces)} 面")
        
        return repaired_vertices, repaired_faces
        
    except Exception as e:
        print(f"表面修复失败: {e}")
        return vertices, faces

def tetgen_pY_with_progressive_repair(vertices, faces):
    """使用渐进式修复策略确保TetGen 'pY'参数成功"""
    print("\n=== TetGen 'pY'参数渐进式修复 ===")
    
    # 策略1: 直接尝试'pY'
    print("\n策略1: 直接使用原始数据尝试'pY'")
    result = try_tetgen_pY(vertices, faces, "原始数据")
    if result is not None:
        return result
    
    # 策略2: 基础清理后尝试'pY'
    print("\n策略2: 基础清理后尝试'pY'")
    clean_vertices, clean_faces = basic_cleanup(vertices, faces)
    result = try_tetgen_pY(clean_vertices, clean_faces, "基础清理")
    if result is not None:
        return result
    
    # 策略3: 流形修复后尝试'pY'
    print("\n策略3: 流形修复后尝试'pY'")
    manifold_vertices, manifold_faces = make_surface_manifold_for_tetgen(clean_vertices, clean_faces)
    result = try_tetgen_pY(manifold_vertices, manifold_faces, "流形修复")
    if result is not None:
        return result
    
    # 策略4: 使用'pY'的变体参数
    print("\n策略4: 尝试'pY'的变体参数")
    pY_variants = [
        ('pYnn', 'pY + 无新增点'),
        ('pYV', 'pY + 详细输出'),
        ('pYA', 'pY + 自适应'),
        ('pYS', 'pY + 静默模式'),
        ('pYC', 'pY + 检查一致性')
    ]
    
    for variant, description in pY_variants:
        try:
            print(f"  尝试 {description} (参数: '{variant}')...")
            tet = tetgen.TetGen(manifold_vertices, manifold_faces)
            tet.tetrahedralize(variant)
            
            tetrahedral_mesh = tet.grid
            if tetrahedral_mesh.n_cells > 0:
                print(f"  ✓ {description} 成功!")
                print(f"    结果: {tetrahedral_mesh.n_points} 顶点, {tetrahedral_mesh.n_cells} 四面体")
                return tetrahedral_mesh
            else:
                print(f"  ✗ {description} 结果为空")
                
        except Exception as e:
            print(f"  ✗ {description} 失败: {str(e)[:50]}...")
            continue
    
    print("所有'pY'相关策略都失败了")
    return None

def basic_cleanup(vertices, faces):
    """基础几何清理"""
    print("进行基础几何清理...")
    
    # 去除完全重复的顶点
    tolerance = 1e-12
    unique_vertices = []
    vertex_map = {}
    
    for i, vertex in enumerate(vertices):
        found_duplicate = False
        for j, unique_vertex in enumerate(unique_vertices):
            if np.allclose(vertex, unique_vertex, atol=tolerance, rtol=1e-12):
                vertex_map[i] = j
                found_duplicate = True
                break
        
        if not found_duplicate:
            vertex_map[i] = len(unique_vertices)
            unique_vertices.append(vertex)
    
    unique_vertices = np.array(unique_vertices)
    
    # 更新面索引
    valid_faces = []
    for face in faces:
        try:
            new_face = [vertex_map[face[0]], vertex_map[face[1]], vertex_map[face[2]]]
            if len(set(new_face)) == 3:  # 三个不同顶点
                valid_faces.append(new_face)
        except (KeyError, IndexError):
            continue
    
    valid_faces = np.array(valid_faces)
    
    removed_vertices = len(vertices) - len(unique_vertices)
    removed_faces = len(faces) - len(valid_faces)
    
    print(f"基础清理: 移除 {removed_vertices} 重复顶点, {removed_faces} 无效面")
    
    return unique_vertices, valid_faces

def try_tetgen_pY(vertices, faces, strategy_name):
    """尝试TetGen 'pY'参数"""
    try:
        print(f"  {strategy_name} - 创建TetGen对象...")
        tet = tetgen.TetGen(vertices, faces)
        
        print(f"  {strategy_name} - 执行'pY'四面体化...")
        tet.tetrahedralize('pY')
        
        tetrahedral_mesh = tet.grid
        
        if tetrahedral_mesh.n_cells > 0:
            print(f"  ✓ {strategy_name} - 'pY'参数成功!")
            print(f"    结果: {tetrahedral_mesh.n_points} 顶点, {tetrahedral_mesh.n_cells} 四面体")
            return tetrahedral_mesh
        else:
            print(f"  ✗ {strategy_name} - 'pY'参数结果为空")
            return None
            
    except Exception as e:
        error_msg = str(e)
        if "Failed to tetrahedralize" in error_msg:
            if "manifold" in error_msg:
                print(f"  ✗ {strategy_name} - 'pY'失败: 非流形表面")
            elif "self-intersections" in error_msg:
                print(f"  ✗ {strategy_name} - 'pY'失败: 自相交")
            else:
                print(f"  ✗ {strategy_name} - 'pY'失败: 四面体化失败")
        else:
            print(f"  ✗ {strategy_name} - 'pY'失败: {error_msg[:50]}...")
        return None

def write_ts_file(file_path, vertices, faces, name=None):
    """写入TS文件"""
    try:
        if name is None:
            name = os.path.splitext(os.path.basename(file_path))[0]
        
        print(f"正在写入TS文件: {file_path}")
        
        with open(file_path, "w", encoding='utf-8') as file:
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("}\n")
            file.write("TFACE\n")
            
            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            
            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            file.write("END\n")
        
        print(f"✓ 成功写入TS文件")
        return True
    except Exception as e:
        print(f"写入TS文件失败: {e}")
        return False

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts 专用TetGen 'pY'参数四面体化脚本")
    print("专门确保TetGen 'pY'参数成功执行")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        return False
    
    # 创建输出目录
    output_dir = "tetgen_pY_output"
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")
    
    start_time = time.time()
    
    # 步骤1: 读取文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_tsurf_data(input_file)
    
    if len(vertices) == 0 or len(faces) == 0:
        print("错误: 无法读取输入文件")
        return False
    
    print(f"✓ 读取完成: {len(vertices)} 顶点, {len(faces)} 面")
    
    # 步骤2: 渐进式TetGen 'pY'尝试
    print(f"\n步骤2: 渐进式TetGen 'pY'尝试...")
    tet_mesh = tetgen_pY_with_progressive_repair(vertices, faces)
    
    if tet_mesh is None:
        print("错误: 所有'pY'策略都失败了")
        return False
    
    # 步骤3: 提取表面并保存
    print(f"\n步骤3: 提取表面并保存...")
    
    if PYVISTA_AVAILABLE:
        # 提取表面
        surface = tet_mesh.extract_surface()
        surface = surface.triangulate()
        
        # 获取表面数据
        surface_vertices = surface.points
        surface_faces = []
        for i in range(surface.n_cells):
            cell = surface.get_cell(i)
            if cell.n_points == 3:
                surface_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
        
        surface_faces = np.array(surface_faces)
        
        # 保存TS文件
        output_ts_file = os.path.join(output_dir, "q_1208_tetgen_pY_surface.ts")
        success = write_ts_file(output_ts_file, surface_vertices, surface_faces, "q_1208_tetgen_pY_surface")
        
        # 保存VTK文件
        surface.save(os.path.join(output_dir, "q_1208_tetgen_pY_surface.vtk"))
        tet_mesh.save(os.path.join(output_dir, "q_1208_tetgen_pY_volume.vtk"))
        
        # 统计信息
        end_time = time.time()
        print(f"\n" + "=" * 60)
        print("TetGen 'pY'参数四面体化完成:")
        print(f"原始网格: {len(vertices)} 顶点, {len(faces)} 面")
        print(f"四面体网格: {tet_mesh.n_points} 顶点, {tet_mesh.n_cells} 四面体")
        print(f"提取表面: {len(surface_vertices)} 顶点, {len(surface_faces)} 面")
        print(f"处理时间: {end_time - start_time:.2f} 秒")
        print(f"输出文件: {output_ts_file}")
        print("✓ 成功使用TetGen 'pY'参数!")
        print("=" * 60)
        
        return True
    else:
        print("PyVista不可用，无法提取表面")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ TetGen 'pY'专用脚本执行成功!")
            sys.exit(0)
        else:
            print("\n✗ TetGen 'pY'专用脚本执行失败!")
            sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        sys.exit(1)
