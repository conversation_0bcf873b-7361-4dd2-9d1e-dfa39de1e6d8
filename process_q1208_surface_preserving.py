#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
q_1208.ts文件表面保持四面体化脚本
专注于使用TetGen的'pY'参数保持原始表面形态
"""

import numpy as np
import os
import sys
import time
from datetime import datetime

# 尝试导入必要的库
try:
    import pyvista as pv
    PYVISTA_AVAILABLE = True
    print("✓ PyVista 可用")
except ImportError:
    PYVISTA_AVAILABLE = False
    print("✗ PyVista 不可用")

try:
    import tetgen
    TETGEN_AVAILABLE = True
    print("✓ TetGen 可用")
except ImportError:
    TETGEN_AVAILABLE = False
    print("✗ TetGen 不可用")
    sys.exit(1)

def read_tsurf_data(file_path):
    """读取TS文件数据"""
    print(f"正在读取文件: {file_path}")
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                lines = file.readlines()
                
            print(f"文件总行数: {len(lines)}")
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x = float(parts[2])
                            y = float(parts[3])
                            z = float(parts[4])
                            vrtx.append([x, y, z])
                            vrtx_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1 = int(parts[1])
                            v2 = int(parts[2])
                            v3 = int(parts[3])
                            
                            if v1 in vrtx_map and v2 in vrtx_map and v3 in vrtx_map:
                                trgl.append([vrtx_map[v1], vrtx_map[v2], vrtx_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                if (i + 1) % 10000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vrtx, dtype=np.float64)
            faces = np.array(trgl, dtype=np.int32)
            
            print(f"成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取文件时出错: {e}")
            continue
    
    return np.array([]), np.array([])

def minimal_cleanup_for_tetgen(vertices, faces):
    """为TetGen进行最小必要的清理，保持原始形态"""
    print("为TetGen进行最小必要清理...")
    
    # 只移除完全重复的顶点（使用极小容差）
    tolerance = 1e-14
    unique_vertices = []
    vertex_map = {}
    
    for i, vertex in enumerate(vertices):
        found_duplicate = False
        for j, unique_vertex in enumerate(unique_vertices):
            if np.allclose(vertex, unique_vertex, atol=tolerance, rtol=1e-14):
                vertex_map[i] = j
                found_duplicate = True
                break
        
        if not found_duplicate:
            vertex_map[i] = len(unique_vertices)
            unique_vertices.append(vertex)
    
    unique_vertices = np.array(unique_vertices)
    
    # 更新面索引，只移除明显无效的面
    valid_faces = []
    for face in faces:
        try:
            new_face = [vertex_map[face[0]], vertex_map[face[1]], vertex_map[face[2]]]
            # 只检查三个顶点是否不同
            if len(set(new_face)) == 3:
                valid_faces.append(new_face)
        except (KeyError, IndexError):
            continue
    
    valid_faces = np.array(valid_faces)
    
    removed_vertices = len(vertices) - len(unique_vertices)
    removed_faces = len(faces) - len(valid_faces)
    
    print(f"清理结果: 移除 {removed_vertices} 重复顶点, {removed_faces} 无效面")
    print(f"最终: {len(unique_vertices)} 顶点, {len(valid_faces)} 面")
    
    return unique_vertices, valid_faces

def tetgen_surface_preserving(vertices, faces):
    """使用TetGen的表面保持参数进行四面体化"""
    print("\n=== TetGen表面保持四面体化 ===")
    
    if not TETGEN_AVAILABLE:
        print("TetGen不可用!")
        return None
    
    try:
        # 创建TetGen对象
        tet = tetgen.TetGen(vertices, faces)
        print("TetGen对象创建成功")
        
        # 按优先级尝试不同的表面保持参数
        surface_preserving_options = [
            ('pY', '纯表面保持'),
            ('pYnn', '表面保持+无新增点'),
            ('pYq2.0', '表面保持+质量约束2.0'),
            ('pYq1.8', '表面保持+质量约束1.8'),
            ('pYq1.5', '表面保持+质量约束1.5'),
            ('pYV', '表面保持+详细输出')
        ]
        
        for option, description in surface_preserving_options:
            try:
                print(f"\n尝试 {description} (参数: '{option}')...")
                
                # 重新创建TetGen对象以避免状态问题
                tet = tetgen.TetGen(vertices, faces)
                
                # 执行四面体化
                tet.tetrahedralize(option)
                
                tetrahedral_mesh = tet.grid
                
                if tetrahedral_mesh.n_cells > 0:
                    print(f"✓ {description} 成功!")
                    print(f"  结果: {tetrahedral_mesh.n_points} 顶点, {tetrahedral_mesh.n_cells} 四面体")
                    return tetrahedral_mesh
                else:
                    print(f"✗ {description} 结果为空")
                    
            except Exception as e:
                error_msg = str(e)
                if "Failed to tetrahedralize" in error_msg:
                    print(f"✗ {description} 失败: 四面体化失败")
                    if "self-intersections" in error_msg:
                        print("  原因: 检测到自相交")
                    if "manifold" in error_msg:
                        print("  原因: 非流形表面")
                else:
                    print(f"✗ {description} 失败: {error_msg[:100]}...")
                continue
        
        print("所有表面保持方法都失败了")
        return None
        
    except Exception as e:
        print(f"TetGen处理出错: {e}")
        return None

def write_ts_file(file_path, vertices, faces, name=None):
    """写入TS文件"""
    try:
        if name is None:
            name = os.path.splitext(os.path.basename(file_path))[0]
        
        print(f"正在写入TS文件: {file_path}")
        
        with open(file_path, "w", encoding='utf-8') as file:
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("}\n")
            file.write("TFACE\n")
            
            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            
            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            file.write("END\n")
        
        print(f"✓ 成功写入TS文件")
        return True
    except Exception as e:
        print(f"写入TS文件失败: {e}")
        return False

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts 表面保持四面体化脚本")
    print("专注于使用TetGen 'pY'参数保持原始表面形态")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        return False
    
    # 创建输出目录
    output_dir = "surface_preserving_output"
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")
    
    start_time = time.time()
    
    # 步骤1: 读取文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_tsurf_data(input_file)
    
    if len(vertices) == 0 or len(faces) == 0:
        print("错误: 无法读取输入文件")
        return False
    
    print(f"✓ 读取完成: {len(vertices)} 顶点, {len(faces)} 面")
    
    # 步骤2: 最小清理
    print(f"\n步骤2: 最小必要清理...")
    clean_vertices, clean_faces = minimal_cleanup_for_tetgen(vertices, faces)
    
    # 步骤3: 表面保持四面体化
    print(f"\n步骤3: 表面保持四面体化...")
    tet_mesh = tetgen_surface_preserving(clean_vertices, clean_faces)
    
    if tet_mesh is None:
        print("错误: 表面保持四面体化失败")
        return False
    
    # 步骤4: 提取表面并保存
    print(f"\n步骤4: 提取表面并保存...")
    
    if PYVISTA_AVAILABLE:
        # 提取表面
        surface = tet_mesh.extract_surface()
        surface = surface.triangulate()
        
        # 获取表面数据
        surface_vertices = surface.points
        surface_faces = []
        for i in range(surface.n_cells):
            cell = surface.get_cell(i)
            if cell.n_points == 3:
                surface_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
        
        surface_faces = np.array(surface_faces)
        
        # 保存TS文件
        output_ts_file = os.path.join(output_dir, "q_1208_surface_preserved.ts")
        success = write_ts_file(output_ts_file, surface_vertices, surface_faces, "q_1208_surface_preserved")
        
        # 保存VTK文件
        surface.save(os.path.join(output_dir, "q_1208_surface_preserved.vtk"))
        tet_mesh.save(os.path.join(output_dir, "q_1208_tetrahedral_volume.vtk"))
        
        # 统计信息
        end_time = time.time()
        print(f"\n" + "=" * 60)
        print("表面保持四面体化完成:")
        print(f"原始网格: {len(vertices)} 顶点, {len(faces)} 面")
        print(f"清理后: {len(clean_vertices)} 顶点, {len(clean_faces)} 面")
        print(f"四面体网格: {tet_mesh.n_points} 顶点, {tet_mesh.n_cells} 四面体")
        print(f"提取表面: {len(surface_vertices)} 顶点, {len(surface_faces)} 面")
        print(f"处理时间: {end_time - start_time:.2f} 秒")
        print(f"输出文件: {output_ts_file}")
        print("=" * 60)
        
        return True
    else:
        print("PyVista不可用，无法提取表面")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 表面保持脚本执行成功!")
            sys.exit(0)
        else:
            print("\n✗ 表面保持脚本执行失败!")
            sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        sys.exit(1)
