#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
q_1208.ts文件表面重建和TetGen 'pY'参数四面体化脚本
通过表面重建技术创建闭合表面，然后使用TetGen 'pY'参数
"""

import numpy as np
import os
import sys
import time
from datetime import datetime

# 尝试导入必要的库
try:
    import pyvista as pv
    PYVISTA_AVAILABLE = True
    print("✓ PyVista 可用")
except ImportError:
    PYVISTA_AVAILABLE = False
    print("✗ PyVista 不可用")
    sys.exit(1)

try:
    import tetgen
    TETGEN_AVAILABLE = True
    print("✓ TetGen 可用")
except ImportError:
    TETGEN_AVAILABLE = False
    print("✗ TetGen 不可用")
    sys.exit(1)

def read_tsurf_data(file_path):
    """读取TS文件数据"""
    print(f"正在读取文件: {file_path}")
    
    encodings = ['utf-8', 'gbk', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            vrtx = []
            vrtx_map = {}
            trgl = []
            current_idx = 0
            
            with open(file_path, 'r', encoding=encoding) as file:
                lines = file.readlines()
                
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line.startswith('VRTX'):
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            vrtx_id = int(parts[1])
                            x = float(parts[2])
                            y = float(parts[3])
                            z = float(parts[4])
                            vrtx.append([x, y, z])
                            vrtx_map[vrtx_id] = current_idx
                            current_idx += 1
                        except (ValueError, IndexError):
                            continue
                
                elif line.startswith('TRGL'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            v1 = int(parts[1])
                            v2 = int(parts[2])
                            v3 = int(parts[3])
                            
                            if v1 in vrtx_map and v2 in vrtx_map and v3 in vrtx_map:
                                trgl.append([vrtx_map[v1], vrtx_map[v2], vrtx_map[v3]])
                        except (ValueError, IndexError):
                            continue
                
                if (i + 1) % 10000 == 0:
                    print(f"已处理 {i + 1} 行...")
            
            vertices = np.array(vrtx, dtype=np.float64)
            faces = np.array(trgl, dtype=np.int32)
            
            print(f"成功读取: {len(vertices)} 个顶点, {len(faces)} 个面")
            return vertices, faces
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取文件时出错: {e}")
            continue
    
    return np.array([]), np.array([])

def reconstruct_closed_surface(vertices, faces):
    """使用表面重建技术创建闭合表面"""
    print("\n=== 表面重建创建闭合表面 ===")
    
    try:
        # 方法1: 使用Poisson表面重建
        print("方法1: 尝试Poisson表面重建...")
        result = poisson_surface_reconstruction(vertices, faces)
        if result is not None:
            return result
        
        # 方法2: 使用Delaunay 3D + 表面提取
        print("方法2: 尝试Delaunay 3D表面重建...")
        result = delaunay_surface_reconstruction(vertices, faces)
        if result is not None:
            return result
        
        # 方法3: 使用凸包重建
        print("方法3: 尝试凸包表面重建...")
        result = convex_hull_reconstruction(vertices)
        if result is not None:
            return result
        
        # 方法4: 使用Alpha Shape重建
        print("方法4: 尝试Alpha Shape表面重建...")
        result = alpha_shape_reconstruction(vertices)
        if result is not None:
            return result
        
        print("所有表面重建方法都失败了")
        return None
        
    except Exception as e:
        print(f"表面重建出错: {e}")
        return None

def poisson_surface_reconstruction(vertices, faces):
    """Poisson表面重建"""
    try:
        # 创建原始网格
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        
        mesh = pv.PolyData(vertices, faces_with_header)
        
        # 计算法向量
        mesh = mesh.compute_normals()
        
        # 尝试Poisson重建（如果PyVista支持）
        try:
            # 检查是否有Poisson重建方法
            if hasattr(mesh, 'reconstruct_surface'):
                reconstructed = mesh.reconstruct_surface()
                print(f"  Poisson重建成功: {reconstructed.n_points} 顶点, {reconstructed.n_cells} 面")
                return extract_vertices_faces(reconstructed)
            else:
                print("  PyVista不支持Poisson重建")
                return None
        except Exception as e:
            print(f"  Poisson重建失败: {e}")
            return None
            
    except Exception as e:
        print(f"  Poisson重建出错: {e}")
        return None

def delaunay_surface_reconstruction(vertices, faces):
    """使用Delaunay 3D进行表面重建"""
    try:
        # 创建点云
        point_cloud = pv.PolyData(vertices)
        
        # 执行Delaunay 3D
        delaunay_mesh = point_cloud.delaunay_3d(alpha=0, tol=1e-05, offset=2.5)
        
        if delaunay_mesh.n_cells > 0:
            # 提取表面
            surface = delaunay_mesh.extract_surface()
            
            # 检查是否闭合
            boundaries = surface.extract_feature_edges(boundary_edges=True, 
                                                      non_manifold_edges=False, 
                                                      manifold_edges=False)
            
            print(f"  Delaunay重建: {surface.n_points} 顶点, {surface.n_cells} 面")
            print(f"  边界边数: {boundaries.n_cells}")
            
            if boundaries.n_cells == 0:
                print("  ✓ Delaunay重建成功创建闭合表面")
                return extract_vertices_faces(surface)
            else:
                print("  ⚠ Delaunay重建表面仍不闭合")
                # 即使不完全闭合，也可能比原始表面好
                return extract_vertices_faces(surface)
        else:
            print("  Delaunay重建失败")
            return None
            
    except Exception as e:
        print(f"  Delaunay重建出错: {e}")
        return None

def convex_hull_reconstruction(vertices):
    """使用凸包进行表面重建"""
    try:
        # 创建点云
        point_cloud = pv.PolyData(vertices)
        
        # 生成凸包
        convex_hull = point_cloud.delaunay_3d().extract_surface()
        
        # 检查凸包
        boundaries = convex_hull.extract_feature_edges(boundary_edges=True, 
                                                      non_manifold_edges=False, 
                                                      manifold_edges=False)
        
        print(f"  凸包重建: {convex_hull.n_points} 顶点, {convex_hull.n_cells} 面")
        print(f"  边界边数: {boundaries.n_cells}")
        
        if boundaries.n_cells == 0:
            print("  ✓ 凸包重建成功创建闭合表面")
            return extract_vertices_faces(convex_hull)
        else:
            print("  ⚠ 凸包重建表面不闭合")
            return None
            
    except Exception as e:
        print(f"  凸包重建出错: {e}")
        return None

def alpha_shape_reconstruction(vertices):
    """使用Alpha Shape进行表面重建"""
    try:
        # 创建点云
        point_cloud = pv.PolyData(vertices)
        
        # 尝试不同的alpha值
        alpha_values = [0, 0.1, 0.5, 1.0, 2.0]
        
        for alpha in alpha_values:
            try:
                print(f"    尝试alpha={alpha}...")
                alpha_mesh = point_cloud.delaunay_3d(alpha=alpha)
                
                if alpha_mesh.n_cells > 0:
                    surface = alpha_mesh.extract_surface()
                    
                    boundaries = surface.extract_feature_edges(boundary_edges=True, 
                                                              non_manifold_edges=False, 
                                                              manifold_edges=False)
                    
                    print(f"    alpha={alpha}: {surface.n_points} 顶点, {surface.n_cells} 面, {boundaries.n_cells} 边界边")
                    
                    if boundaries.n_cells == 0:
                        print(f"  ✓ Alpha Shape (alpha={alpha}) 成功创建闭合表面")
                        return extract_vertices_faces(surface)
                        
            except Exception as e:
                print(f"    alpha={alpha} 失败: {e}")
                continue
        
        print("  所有Alpha值都失败了")
        return None
        
    except Exception as e:
        print(f"  Alpha Shape重建出错: {e}")
        return None

def extract_vertices_faces(mesh):
    """从PyVista网格提取顶点和面"""
    vertices = mesh.points
    faces = []
    
    for i in range(mesh.n_cells):
        cell = mesh.get_cell(i)
        if cell.n_points == 3:
            faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
    
    faces = np.array(faces)
    return vertices, faces

def verify_closed_surface(vertices, faces):
    """验证表面是否闭合"""
    print("\n=== 验证表面闭合性 ===")
    
    try:
        faces_with_header = []
        for face in faces:
            faces_with_header.extend([3, face[0], face[1], face[2]])
        
        mesh = pv.PolyData(vertices, faces_with_header)
        
        # 检查边界边
        boundaries = mesh.extract_feature_edges(boundary_edges=True, 
                                               non_manifold_edges=False, 
                                               manifold_edges=False)
        
        # 检查非流形边
        non_manifold = mesh.extract_feature_edges(boundary_edges=False, 
                                                 non_manifold_edges=True, 
                                                 manifold_edges=False)
        
        print(f"边界边数量: {boundaries.n_cells}")
        print(f"非流形边数量: {non_manifold.n_cells}")
        
        is_closed = (boundaries.n_cells == 0 and non_manifold.n_cells == 0)
        
        if is_closed:
            print("✓ 表面是闭合流形，适合TetGen 'pY'参数")
        else:
            print("⚠ 表面仍有问题")
        
        return is_closed
        
    except Exception as e:
        print(f"验证失败: {e}")
        return False

def tetgen_pY_only(vertices, faces):
    """使用TetGen 'pY'参数进行四面体化"""
    print("\n=== TetGen 'pY'参数四面体化 ===")
    
    try:
        print("创建TetGen对象...")
        tet = tetgen.TetGen(vertices, faces)
        
        print("执行'pY'参数四面体化...")
        tet.tetrahedralize('pY')
        
        tetrahedral_mesh = tet.grid
        
        if tetrahedral_mesh.n_cells > 0:
            print(f"✓ 'pY'参数四面体化成功!")
            print(f"  结果: {tetrahedral_mesh.n_points} 顶点, {tetrahedral_mesh.n_cells} 四面体")
            return tetrahedral_mesh
        else:
            print("✗ 'pY'参数四面体化结果为空")
            return None
            
    except Exception as e:
        print(f"✗ 'pY'参数四面体化失败: {e}")
        return None

def write_ts_file(file_path, vertices, faces, name=None):
    """写入TS文件"""
    try:
        if name is None:
            name = os.path.splitext(os.path.basename(file_path))[0]
        
        print(f"正在写入TS文件: {file_path}")
        
        with open(file_path, "w", encoding='utf-8') as file:
            file.write("GOCAD TSurf 1 \n")
            file.write("HEADER {\n")
            file.write(f"name: {name}\n")
            file.write("*solid*color: #f0e68c\n")
            file.write("}\n")
            file.write("TFACE\n")
            
            for i, v in enumerate(vertices):
                file.write(f"VRTX {i+1} {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            
            for face in faces:
                file.write(f"TRGL {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            file.write("END\n")
        
        print(f"✓ 成功写入TS文件")
        return True
    except Exception as e:
        print(f"写入TS文件失败: {e}")
        return False

def main():
    """主处理函数"""
    print("=" * 60)
    print("q_1208.ts 表面重建和TetGen 'pY'参数四面体化脚本")
    print("通过表面重建技术创建闭合表面")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件
    input_file = "q_1208.ts"
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        return False
    
    # 创建输出目录
    output_dir = "surface_reconstruction_output"
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")
    
    start_time = time.time()
    
    # 步骤1: 读取文件
    print(f"\n步骤1: 读取TS文件...")
    vertices, faces = read_tsurf_data(input_file)
    
    if len(vertices) == 0 or len(faces) == 0:
        print("错误: 无法读取输入文件")
        return False
    
    print(f"✓ 读取完成: {len(vertices)} 顶点, {len(faces)} 面")
    
    # 步骤2: 表面重建
    print(f"\n步骤2: 表面重建...")
    reconstructed_result = reconstruct_closed_surface(vertices, faces)
    
    if reconstructed_result is None:
        print("错误: 表面重建失败")
        return False
    
    reconstructed_vertices, reconstructed_faces = reconstructed_result
    print(f"✓ 表面重建完成: {len(reconstructed_vertices)} 顶点, {len(reconstructed_faces)} 面")
    
    # 保存重建后的表面
    reconstructed_ts_file = os.path.join(output_dir, "q_1208_reconstructed_surface.ts")
    write_ts_file(reconstructed_ts_file, reconstructed_vertices, reconstructed_faces, "q_1208_reconstructed_surface")
    
    # 步骤3: 验证闭合性
    print(f"\n步骤3: 验证重建表面...")
    is_closed = verify_closed_surface(reconstructed_vertices, reconstructed_faces)
    
    # 步骤4: TetGen 'pY'四面体化
    print(f"\n步骤4: TetGen 'pY'四面体化...")
    tet_mesh = tetgen_pY_only(reconstructed_vertices, reconstructed_faces)
    
    if tet_mesh is None:
        print("错误: 'pY'参数四面体化失败")
        return False
    
    # 步骤5: 提取表面并保存
    print(f"\n步骤5: 提取表面并保存...")
    
    # 提取表面
    surface = tet_mesh.extract_surface()
    surface = surface.triangulate()
    
    # 获取表面数据
    surface_vertices = surface.points
    surface_faces = []
    for i in range(surface.n_cells):
        cell = surface.get_cell(i)
        if cell.n_points == 3:
            surface_faces.append([cell.point_ids[0], cell.point_ids[1], cell.point_ids[2]])
    
    surface_faces = np.array(surface_faces)
    
    # 保存最终结果
    output_ts_file = os.path.join(output_dir, "q_1208_final_pY_result.ts")
    success = write_ts_file(output_ts_file, surface_vertices, surface_faces, "q_1208_final_pY_result")
    
    # 保存VTK文件
    surface.save(os.path.join(output_dir, "q_1208_final_surface.vtk"))
    tet_mesh.save(os.path.join(output_dir, "q_1208_final_volume.vtk"))
    
    # 统计信息
    end_time = time.time()
    print(f"\n" + "=" * 60)
    print("表面重建和TetGen 'pY'参数四面体化完成:")
    print(f"原始网格: {len(vertices)} 顶点, {len(faces)} 面")
    print(f"重建表面: {len(reconstructed_vertices)} 顶点, {len(reconstructed_faces)} 面")
    print(f"四面体网格: {tet_mesh.n_points} 顶点, {tet_mesh.n_cells} 四面体")
    print(f"最终表面: {len(surface_vertices)} 顶点, {len(surface_faces)} 面")
    print(f"处理时间: {end_time - start_time:.2f} 秒")
    print(f"重建表面文件: {reconstructed_ts_file}")
    print(f"最终结果文件: {output_ts_file}")
    print("✓ 成功通过表面重建使用TetGen 'pY'参数!")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 表面重建脚本执行成功!")
            sys.exit(0)
        else:
            print("\n✗ 表面重建脚本执行失败!")
            sys.exit(1)
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        sys.exit(1)
